System.register(["react"],function(o){"use strict";var c;return{setters:[function(s){c=s.default}],execute:function(){o("useShallow",u);function s(r,t){if(Object.is(r,t))return!0;if(typeof r!="object"||r===null||typeof t!="object"||t===null)return!1;if(r instanceof Map&&t instanceof Map){if(r.size!==t.size)return!1;for(const[e,i]of r)if(!Object.is(i,t.get(e)))return!1;return!0}if(r instanceof Set&&t instanceof Set){if(r.size!==t.size)return!1;for(const e of r)if(!t.has(e))return!1;return!0}const n=Object.keys(r);if(n.length!==Object.keys(t).length)return!1;for(const e of n)if(!Object.prototype.hasOwnProperty.call(t,e)||!Object.is(r[e],t[e]))return!1;return!0}const{useRef:f}=c;function u(r){const t=f();return n=>{const e=r(n);return s(t.current,e)?t.current:t.current=e}}}}});
