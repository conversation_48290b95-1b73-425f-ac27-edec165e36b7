# Installation
> `npm install --save @types/marked`

# Summary
This package contains type definitions for Mark<PERSON> (https://github.com/markedjs/marked).

# Details
Files were exported from https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/marked.

### Additional Details
 * Last updated: Wed, 27 Sep 2023 09:42:15 GMT
 * Dependencies: none
 * Global values: none

# Credits
These definitions were written by [<PERSON>](https://github.com/worr), [<PERSON>ing<PERSON><PERSON>](https://github.com/BendingBender), [<PERSON>R](https://github.com/CrossR), [<PERSON>](https://github.com/mwickett), [<PERSON><PERSON>](https://github.com/htkzhtm), [<PERSON>](https://github.com/ezracelli), [<PERSON><PERSON> BARO](https://github.com/scandinave), [<PERSON><PERSON>](https://github.com/sarunint), [<PERSON>](https://github.com/UziTech), [<PERSON><PERSON><PERSON><PERSON>](https://github.com/<PERSON>), [<PERSON><PERSON><PERSON><PERSON>](https://github.com/jfcere), and [Mykhaylo <PERSON>olyarchuk](https://github.com/MykSto).
