const { spawn } = require('child_process');
const path = require('path');

console.log('Starting StdMD application...');

// Start the built application
const electronPath = path.join(__dirname, 'node_modules', '.bin', 'electron');
const appPath = path.join(__dirname, 'dist', 'main', 'main.js');

const app = spawn(electronPath, [appPath], {
  stdio: 'inherit',
  cwd: __dirname
});

app.on('close', (code) => {
  console.log(`StdMD application exited with code ${code}`);
});

app.on('error', (err) => {
  console.error('Failed to start StdMD application:', err);
});
