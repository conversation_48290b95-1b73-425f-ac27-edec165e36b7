# StdMD 部署指南

本文档详细说明如何构建、打包和分发StdMD应用程序。

## 开发环境要求

### 系统要求
- **Node.js**: 18.0.0 或更高版本
- **npm**: 8.0.0 或更高版本
- **Git**: 用于版本控制

### 平台特定要求

#### Windows
- Windows 10 或更高版本
- Visual Studio Build Tools 或 Visual Studio Community
- Python 3.x (用于某些native模块编译)

#### macOS
- macOS 10.14 (Mojave) 或更高版本
- Xcode Command Line Tools
- 对于代码签名: Apple Developer账户

#### Linux
- Ubuntu 18.04+ / Fedora 32+ / 或等效发行版
- 构建工具: `build-essential`, `libnss3-dev`, `libatk-bridge2.0-dev`, `libdrm2`, `libxcomposite1`, `libxdamage1`, `libxrandr2`, `libgbm1`, `libxss1`, `libasound2`

## 快速开始

### 1. 克隆项目
```bash
git clone <repository-url>
cd StdMD
```

### 2. 安装依赖
```bash
npm install
```

### 3. 开发模式运行
```bash
npm run dev
```

### 4. 构建应用
```bash
npm run build
```

### 5. 启动应用
```bash
npm start
```

## 详细构建流程

### 开发构建
```bash
# 启动开发服务器 (热重载)
npm run dev

# 单独构建主进程
npm run dev:main

# 单独构建渲染进程
npm run dev:renderer
```

### 生产构建
```bash
# 完整构建
npm run build

# 清理构建缓存
npm run clean

# 构建图标
npm run build:icons
```

## 应用打包

### 本地打包 (不分发)
```bash
npm run pack
```

### 分发打包

#### 所有平台
```bash
npm run dist:all
```

#### 特定平台
```bash
# Windows
npm run dist:win

# macOS
npm run dist:mac

# Linux
npm run dist:linux
```

### 打包输出

打包完成后，安装包将生成在 `build/dist/` 目录中：

- **Windows**: `StdMD Setup 1.0.0.exe` (NSIS安装包) 和 `StdMD 1.0.0.exe` (便携版)
- **macOS**: `StdMD-1.0.0.dmg` (磁盘镜像) 和 `StdMD-1.0.0-mac.zip` (压缩包)
- **Linux**: `StdMD-1.0.0.AppImage` (AppImage), `stdmd_1.0.0_amd64.deb` (Debian包), `stdmd-1.0.0.x86_64.rpm` (RPM包)

## 代码签名 (可选)

### macOS代码签名
1. 获取Apple Developer证书
2. 在 `package.json` 中配置:
```json
{
  "build": {
    "mac": {
      "identity": "Developer ID Application: Your Name (XXXXXXXXXX)"
    }
  }
}
```

### Windows代码签名
1. 获取代码签名证书
2. 配置环境变量:
```bash
set CSC_LINK=path/to/certificate.p12
set CSC_KEY_PASSWORD=certificate_password
```

## 持续集成/持续部署 (CI/CD)

### GitHub Actions 示例

创建 `.github/workflows/build.yml`:

```yaml
name: Build and Release

on:
  push:
    tags:
      - 'v*'

jobs:
  build:
    runs-on: ${{ matrix.os }}
    strategy:
      matrix:
        os: [windows-latest, macos-latest, ubuntu-latest]

    steps:
    - uses: actions/checkout@v3
    
    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'
        cache: 'npm'
    
    - name: Install dependencies
      run: npm ci
    
    - name: Build application
      run: npm run build
    
    - name: Package application
      run: npm run dist
    
    - name: Upload artifacts
      uses: actions/upload-artifact@v3
      with:
        name: ${{ matrix.os }}-build
        path: build/dist/*
```

## 性能优化

### 构建优化
- 使用 `webpack-bundle-analyzer` 分析包大小
- 启用代码分割和懒加载
- 优化图片和静态资源

### 运行时优化
- 实现虚拟滚动 (大文件列表)
- 使用 Web Workers 处理重计算
- 缓存频繁访问的数据

## 故障排除

### 常见构建问题

#### 1. Node-gyp 编译错误
```bash
# Windows
npm install --global windows-build-tools

# macOS
xcode-select --install

# Linux
sudo apt-get install build-essential
```

#### 2. Electron 下载失败
```bash
# 设置镜像源
npm config set electron_mirror https://npm.taobao.org/mirrors/electron/
```

#### 3. 内存不足
```bash
# 增加Node.js内存限制
export NODE_OPTIONS="--max-old-space-size=4096"
```

### 调试技巧

#### 开发者工具
- 主进程调试: `--inspect` 标志
- 渲染进程调试: F12 开发者工具

#### 日志记录
```javascript
// 主进程日志
console.log('Main process log');

// 渲染进程日志
console.log('Renderer process log');
```

## 发布流程

### 1. 版本管理
```bash
# 更新版本号
npm version patch|minor|major

# 推送标签
git push origin --tags
```

### 2. 生成发布说明
- 更新 `CHANGELOG.md`
- 准备发布说明
- 截图和演示视频

### 3. 分发渠道
- GitHub Releases
- 官方网站下载
- 应用商店 (Microsoft Store, Mac App Store)

## 安全考虑

### 代码安全
- 定期更新依赖包
- 使用 `npm audit` 检查漏洞
- 启用内容安全策略 (CSP)

### 分发安全
- 代码签名所有可执行文件
- 提供校验和文件
- 使用HTTPS分发

## 监控和分析

### 错误报告
- 集成 Sentry 或类似服务
- 收集崩溃报告
- 性能监控

### 使用分析
- 匿名使用统计
- 功能使用频率
- 性能指标

---

## 支持

如果在部署过程中遇到问题，请：

1. 查看 [故障排除](#故障排除) 部分
2. 搜索现有的 [GitHub Issues](https://github.com/stdmd/stdmd/issues)
3. 创建新的Issue并提供详细信息

## 贡献

欢迎贡献代码！请参阅 [CONTRIBUTING.md](CONTRIBUTING.md) 了解详细信息。
