# StdMD 用户指南

欢迎使用StdMD！这是一个功能强大的跨平台Markdown编辑器，专为提高您的写作效率而设计。

## 目录
- [快速开始](#快速开始)
- [界面介绍](#界面介绍)
- [基本操作](#基本操作)
- [高级功能](#高级功能)
- [主题定制](#主题定制)
- [导入导出](#导入导出)
- [快捷键](#快捷键)
- [常见问题](#常见问题)

## 快速开始

### 安装应用
1. 从官方网站下载适合您操作系统的安装包
2. 运行安装程序并按照提示完成安装
3. 启动StdMD应用程序

### 第一次使用
1. **打开工作区**: 点击 `File → Open Folder` 选择包含Markdown文件的文件夹
2. **创建新文件**: 点击 `File → New File` 或使用快捷键 `Ctrl+N`
3. **开始编写**: 在左侧编辑器中输入Markdown内容，右侧会实时显示预览

## 界面介绍

StdMD采用三栏布局设计：

```
┌─────────────┬─────────────────┬─────────────────┐
│             │                 │                 │
│  文件树     │    编辑器       │    预览窗口     │
│  (左侧)     │    (中间)       │    (右侧)       │
│             │                 │                 │
└─────────────┴─────────────────┴─────────────────┘
```

### 工具栏
位于顶部，包含常用功能按钮：
- 📁 **切换侧边栏**: 显示/隐藏文件树
- 👁️ **切换预览**: 显示/隐藏预览窗口
- 📄 **导出PDF**: 打开PDF导出对话框
- 🎨 **主题选择**: 切换应用主题

### 文件树 (左侧)
- 显示当前工作区的文件和文件夹结构
- 支持文件夹展开/折叠
- 不同文件类型有对应的图标
- 点击文件名可在编辑器中打开

### 编辑器 (中间)
- 基于CodeMirror的强大编辑器
- 支持Markdown语法高亮
- 行号显示
- 代码折叠功能
- 搜索和替换

### 预览窗口 (右侧)
- 实时渲染Markdown内容
- 支持GitHub风格的Markdown
- 数学公式渲染
- 表格和代码块美化

### 标签页
- 支持同时打开多个文件
- 显示文件名和修改状态
- 点击 `×` 关闭文件
- 修改过的文件会显示 `●` 标记

## 基本操作

### 文件管理

#### 打开文件
- **单个文件**: `File → Open File` 或 `Ctrl+O`
- **整个文件夹**: `File → Open Folder` 或 `Ctrl+Shift+O`

#### 创建文件
- `File → New File` 或 `Ctrl+N`
- 新文件会以 "Untitled.md" 命名

#### 保存文件
- **保存**: `File → Save` 或 `Ctrl+S`
- **另存为**: `File → Save As` 或 `Ctrl+Shift+S`

#### 关闭文件
- 点击标签页上的 `×` 按钮
- 或使用 `Ctrl+W`

### 编辑功能

#### 基本编辑
- 支持标准的文本编辑操作
- 自动保存功能
- 撤销/重做: `Ctrl+Z` / `Ctrl+Y`

#### 搜索和替换
- **搜索**: `Ctrl+F`
- **替换**: `Ctrl+H`
- 支持正则表达式搜索

#### 代码折叠
- 点击行号旁的折叠图标
- 支持标题和代码块折叠

## 高级功能

### Markdown语法支持

#### 基础语法
```markdown
# 标题
## 二级标题
### 三级标题

**粗体文本**
*斜体文本*
~~删除线~~

- 无序列表
1. 有序列表

[链接](https://example.com)
![图片](image.jpg)

`行内代码`

```代码块```
```

#### 扩展语法
```markdown
| 表格 | 列1 | 列2 |
|------|-----|-----|
| 行1  | 数据 | 数据 |

- [x] 已完成任务
- [ ] 未完成任务

> 引用文本

---
分割线
```

#### 数学公式
```markdown
行内公式: $E = mc^2$

块级公式:
$$
\int_{-\infty}^{\infty} e^{-x^2} dx = \sqrt{\pi}
$$
```

#### 脚注
```markdown
这是一个带脚注的文本[^1]。

[^1]: 这是脚注内容。
```

### 实时预览
- 编辑器中的内容会实时在预览窗口中渲染
- 支持滚动同步
- 预览样式可自定义

## 主题定制

### 内置主题
StdMD提供5种内置主题：
1. **Light** - 明亮主题 (默认)
2. **Dark** - 暗黑主题
3. **Ocean Blue** - 海洋蓝主题
4. **Forest Green** - 森林绿主题
5. **Royal Purple** - 皇家紫主题

### 切换主题
1. 点击工具栏中的 🎨 按钮
2. 在主题选择器中预览不同主题
3. 点击选择您喜欢的主题
4. 点击 "Apply" 应用主题

### 主题特性
- 每个主题都有独特的配色方案
- 自动适配编辑器和预览窗口
- 主题设置会自动保存

## 导入导出

### PDF导出

#### 基本导出
1. 点击工具栏中的 📄 按钮或使用 `Ctrl+E`
2. 在导出对话框中配置选项
3. 点击 "Export PDF" 开始导出

#### 导出选项
- **文档标题**: 设置PDF文档标题
- **作者**: 设置文档作者
- **页面格式**: A4, Letter, Legal
- **方向**: 纵向或横向
- **页边距**: 自定义上下左右边距
- **目录**: 可选择包含目录

### Word文档导入

#### 导入流程
1. 选择 `File → Import Word Document`
2. 选择 `.docx` 文件
3. 系统自动转换为Markdown格式
4. 转换结果在新标签页中显示

#### 支持的转换
- 标题层级
- 文本格式 (粗体、斜体)
- 列表 (有序、无序)
- 表格
- 图片 (转为base64编码)
- 链接

## 快捷键

### 文件操作
- `Ctrl+N` - 新建文件
- `Ctrl+O` - 打开文件
- `Ctrl+Shift+O` - 打开文件夹
- `Ctrl+S` - 保存文件
- `Ctrl+Shift+S` - 另存为
- `Ctrl+W` - 关闭当前文件

### 编辑操作
- `Ctrl+Z` - 撤销
- `Ctrl+Y` - 重做
- `Ctrl+X` - 剪切
- `Ctrl+C` - 复制
- `Ctrl+V` - 粘贴
- `Ctrl+A` - 全选

### 搜索操作
- `Ctrl+F` - 搜索
- `Ctrl+H` - 替换
- `F3` - 查找下一个
- `Shift+F3` - 查找上一个

### 功能操作
- `Ctrl+E` - 导出PDF
- `F11` - 全屏模式
- `Ctrl+,` - 打开设置 (计划中)

## 常见问题

### Q: 如何更改编辑器字体？
A: 目前字体设置通过主题控制，未来版本将支持自定义字体设置。

### Q: 支持哪些图片格式？
A: 支持常见的图片格式：PNG, JPG, JPEG, GIF, SVG, WebP。

### Q: 如何备份我的文档？
A: StdMD是基于本地文件的编辑器，您的文档保存在本地文件系统中。建议定期备份整个工作目录。

### Q: 可以同时编辑多个文件吗？
A: 是的，StdMD支持多标签页编辑，您可以同时打开和编辑多个文件。

### Q: 预览窗口可以隐藏吗？
A: 可以，点击工具栏中的 👁️ 按钮可以切换预览窗口的显示/隐藏。

### Q: 如何报告问题或建议功能？
A: 请访问我们的GitHub仓库提交Issue，或通过官方邮箱联系我们。

---

## 获取帮助

如果您需要更多帮助：

1. 查看本用户指南
2. 访问官方网站的FAQ部分
3. 在GitHub上提交Issue
4. 加入用户社区讨论

感谢您使用StdMD！我们致力于为您提供最佳的Markdown编辑体验。
