# StdMD 测试文档

这是一个测试Markdown文档，用于验证StdMD编辑器的功能。

## 基本语法测试

### 文本格式
- **粗体文本**
- *斜体文本*
- ~~删除线文本~~
- `行内代码`

### 列表
1. 有序列表项1
2. 有序列表项2
3. 有序列表项3

- 无序列表项A
- 无序列表项B
- 无序列表项C

### 链接和图片
[GitHub](https://github.com)

### 代码块
```javascript
function hello() {
    console.log("Hello, StdMD!");
}
```

```python
def greet(name):
    print(f"Hello, {name}!")
```

### 表格
| 功能 | 状态 | 描述 |
|------|------|------|
| 文件管理 | ✅ | 支持多文件编辑 |
| 实时预览 | ✅ | 即时渲染效果 |
| PDF导出 | 🚧 | 开发中 |
| Word导入 | 🚧 | 开发中 |

### 引用
> 这是一个引用块
> 
> 可以包含多行内容

### 分割线
---

## MultiMarkdown扩展

### 脚注
这是一个带脚注的文本[^1]。

[^1]: 这是脚注内容

### 数学公式
行内公式：$E = mc^2$

块级公式：
$$
\int_{-\infty}^{\infty} e^{-x^2} dx = \sqrt{\pi}
$$

### 任务列表
- [x] 完成项目初始化
- [x] 搭建基础框架
- [ ] 实现PDF导出
- [ ] 实现Word导入
- [ ] 添加更多主题

## 测试结果

如果您能看到上述内容正确渲染，说明StdMD的基本功能正常工作！
