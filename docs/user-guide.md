# 用户指南

本指南将详细介绍StdMD的各项功能和使用方法。

## 界面布局

StdMD采用经典的三栏布局：

```
┌─────────────┬─────────────────┬─────────────────┐
│             │                 │                 │
│  文件树     │    编辑器       │    预览窗口     │
│             │                 │                 │
│             │                 │                 │
└─────────────┴─────────────────┴─────────────────┘
```

### 文件树 (左侧)
- 显示当前工作区的文件和文件夹
- 支持文件夹展开/折叠
- 双击文件可在编辑器中打开
- 右键菜单提供更多操作

### 编辑器 (中间)
- 基于CodeMirror的强大编辑器
- 支持Markdown语法高亮
- 行号显示
- 代码折叠
- 搜索替换

### 预览窗口 (右侧)
- 实时渲染Markdown内容
- 支持GitHub风格的Markdown
- 数学公式渲染
- 代码语法高亮

## 文件操作

### 打开文件
- **单个文件**: `File → Open File` 或 `Ctrl+O`
- **工作区**: `File → Open Folder` 或 `Ctrl+Shift+O`

### 保存文件
- **保存**: `File → Save` 或 `Ctrl+S`
- **另存为**: `File → Save As` 或 `Ctrl+Shift+S`

### 文件标签页
- 支持同时打开多个文件
- 标签页显示文件名和修改状态
- 点击 `×` 关闭文件
- 拖拽重新排序

## Markdown语法支持

### 基础语法
- 标题 (`#`, `##`, `###`)
- 粗体 (`**text**`)
- 斜体 (`*text*`)
- 删除线 (`~~text~~`)
- 行内代码 (`` `code` ``)
- 代码块 (``` ``` ```)
- 链接 (`[text](url)`)
- 图片 (`![alt](url)`)
- 列表 (`-`, `1.`)
- 引用 (`>`)
- 分割线 (`---`)

### 扩展语法 (GitHub Flavored Markdown)
- 表格
- 任务列表 (`- [x]`, `- [ ]`)
- 删除线
- 自动链接

### MultiMarkdown支持
- 脚注 (`[^1]`)
- 定义列表
- 表格增强
- 数学公式 (`$...$`, `$$...$$`)
- 元数据

## 导入导出

### PDF导出
1. 打开要导出的Markdown文件
2. 选择 `File → Export as PDF` 或 `Ctrl+E`
3. 选择保存位置和文件名
4. 点击保存

### Word文档导入
1. 选择 `File → Import Word Document`
2. 选择 `.docx` 文件
3. 系统会自动转换为Markdown格式
4. 在新标签页中显示转换结果

## 自定义设置

### 主题切换
- 支持明亮和暗黑主题
- 可自定义编辑器字体和大小
- 预览样式可配置

### 快捷键
- 所有操作都支持键盘快捷键
- 可自定义快捷键组合
- 支持Vim模式 (可选)

## 高级功能

### 文件监听
- 自动检测文件变化
- 外部修改时提示重新加载
- 防止数据丢失

### 搜索功能
- 全文搜索
- 正则表达式支持
- 替换功能

### 插件系统
- 支持第三方插件
- 语法扩展
- 主题插件

## 故障排除

### 常见问题
1. **文件无法打开**: 检查文件权限和格式
2. **预览不显示**: 确认Markdown语法正确
3. **导出失败**: 检查磁盘空间和权限

### 获取帮助
- 查看 [FAQ](faq.md)
- 提交 [Issue](https://github.com/stdmd/issues)
- 加入社区讨论
