(()=>{"use strict";const e=require("electron");e.contextBridge.exposeInMainWorld("electronAPI",{readFile:r=>e.ipcRenderer.invoke("read-file",r),writeFile:(r,n)=>e.ipcRenderer.invoke("write-file",r,n),readDirectory:r=>e.ipcRenderer.invoke("read-directory",r),onMenuNewFile:r=>e.ipcRenderer.on("menu-new-file",r),onMenuSave:r=>e.ipcRenderer.on("menu-save",r),onMenuSaveAs:r=>e.ipcRenderer.on("menu-save-as",r),onMenuExportPdf:r=>e.ipcRenderer.on("menu-export-pdf",r),onFileOpened:r=>e.ipcRenderer.on("file-opened",(e,n)=>r(n)),onFolderOpened:r=>e.ipcRenderer.on("folder-opened",(e,n)=>r(n)),onWordImport:r=>e.ipcRenderer.on("word-import",(e,n)=>r(n)),removeAllListeners:r=>e.ipcRenderer.removeAllListeners(r)})})();