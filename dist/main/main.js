(()=>{"use strict";const e=require("electron"),n=require("path"),i=require("fs");new class{constructor(){this.mainWindow=null,this.initializeApp()}initializeApp(){e.app.whenReady().then(()=>{this.createMainWindow(),this.setupMenu(),this.setupIpcHandlers()}),e.app.on("window-all-closed",()=>{"darwin"!==process.platform&&e.app.quit()}),e.app.on("activate",()=>{0===e.BrowserWindow.getAllWindows().length&&this.createMainWindow()})}createMainWindow(){this.mainWindow=new e.BrowserWindow({width:1200,height:800,minWidth:800,minHeight:600,webPreferences:{nodeIntegration:!1,contextIsolation:!0,preload:n.join(__dirname,"preload.js")},titleBarStyle:"hiddenInset",show:!1}),this.mainWindow.loadFile(n.join(__dirname,"../renderer/index.html")),this.mainWindow.once("ready-to-show",()=>{this.mainWindow?.show()}),this.mainWindow.on("closed",()=>{this.mainWindow=null})}setupMenu(){const n=[{label:"File",submenu:[{label:"New File",accelerator:"CmdOrCtrl+N",click:()=>{this.mainWindow?.webContents.send("menu-new-file")}},{label:"Open File",accelerator:"CmdOrCtrl+O",click:()=>{this.openFile()}},{label:"Open Folder",accelerator:"CmdOrCtrl+Shift+O",click:()=>{this.openFolder()}},{type:"separator"},{label:"Save",accelerator:"CmdOrCtrl+S",click:()=>{this.mainWindow?.webContents.send("menu-save")}},{label:"Save As",accelerator:"CmdOrCtrl+Shift+S",click:()=>{this.mainWindow?.webContents.send("menu-save-as")}},{type:"separator"},{label:"Export as PDF",accelerator:"CmdOrCtrl+E",click:()=>{this.mainWindow?.webContents.send("menu-export-pdf")}},{label:"Import Word Document",click:()=>{this.importWordDocument()}}]},{label:"Edit",submenu:[{role:"undo"},{role:"redo"},{type:"separator"},{role:"cut"},{role:"copy"},{role:"paste"},{role:"selectAll"}]},{label:"View",submenu:[{role:"reload"},{role:"forceReload"},{role:"toggleDevTools"},{type:"separator"},{role:"resetZoom"},{role:"zoomIn"},{role:"zoomOut"},{type:"separator"},{role:"togglefullscreen"}]}],i=e.Menu.buildFromTemplate(n);e.Menu.setApplicationMenu(i)}setupIpcHandlers(){e.ipcMain.handle("read-file",async(e,n)=>{try{return{success:!0,content:await i.promises.readFile(n,"utf-8")}}catch(e){return{success:!1,error:e.message}}}),e.ipcMain.handle("write-file",async(e,n,t)=>{try{return await i.promises.writeFile(n,t,"utf-8"),{success:!0}}catch(e){return{success:!1,error:e.message}}}),e.ipcMain.handle("read-directory",async(e,t)=>{try{return{success:!0,items:(await i.promises.readdir(t,{withFileTypes:!0})).map(e=>({name:e.name,isDirectory:e.isDirectory(),path:n.join(t,e.name)}))}}catch(e){return{success:!1,error:e.message}}})}async openFile(){const n=await e.dialog.showOpenDialog(this.mainWindow,{properties:["openFile"],filters:[{name:"Markdown Files",extensions:["md","markdown","mdown","mkd"]},{name:"All Files",extensions:["*"]}]});!n.canceled&&n.filePaths.length>0&&this.mainWindow?.webContents.send("file-opened",n.filePaths[0])}async openFolder(){const n=await e.dialog.showOpenDialog(this.mainWindow,{properties:["openDirectory"]});!n.canceled&&n.filePaths.length>0&&this.mainWindow?.webContents.send("folder-opened",n.filePaths[0])}async importWordDocument(){const n=await e.dialog.showOpenDialog(this.mainWindow,{properties:["openFile"],filters:[{name:"Word Documents",extensions:["docx"]}]});!n.canceled&&n.filePaths.length>0&&this.mainWindow?.webContents.send("word-import",n.filePaths[0])}}})();