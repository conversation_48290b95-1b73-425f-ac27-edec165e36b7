/*
 * ATTENTION: The "eval" devtool has been used (maybe by default in mode: "development").
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
/******/ (() => { // webpackBootstrap
/******/ 	"use strict";
/******/ 	var __webpack_modules__ = ({

/***/ "./src/main/main.ts":
/*!**************************!*\
  !*** ./src/main/main.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var electron__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! electron */ \"electron\");\n/* harmony import */ var electron__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(electron__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! fs */ \"fs\");\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(fs__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nclass StdMDApp {\n    constructor() {\n        this.mainWindow = null;\n        this.initializeApp();\n    }\n    initializeApp() {\n        electron__WEBPACK_IMPORTED_MODULE_0__.app.whenReady().then(() => {\n            this.createMainWindow();\n            this.setupMenu();\n            this.setupIpcHandlers();\n        });\n        electron__WEBPACK_IMPORTED_MODULE_0__.app.on('window-all-closed', () => {\n            if (process.platform !== 'darwin') {\n                electron__WEBPACK_IMPORTED_MODULE_0__.app.quit();\n            }\n        });\n        electron__WEBPACK_IMPORTED_MODULE_0__.app.on('activate', () => {\n            if (electron__WEBPACK_IMPORTED_MODULE_0__.BrowserWindow.getAllWindows().length === 0) {\n                this.createMainWindow();\n            }\n        });\n    }\n    createMainWindow() {\n        this.mainWindow = new electron__WEBPACK_IMPORTED_MODULE_0__.BrowserWindow({\n            width: 1200,\n            height: 800,\n            minWidth: 800,\n            minHeight: 600,\n            webPreferences: {\n                nodeIntegration: false,\n                contextIsolation: true,\n                preload: path__WEBPACK_IMPORTED_MODULE_1__.join(__dirname, 'preload.js'),\n            },\n            titleBarStyle: 'hiddenInset',\n            show: false,\n        });\n        // Load the app\n        if (true) {\n            this.mainWindow.loadURL('http://localhost:3000');\n            this.mainWindow.webContents.openDevTools();\n        }\n        else // removed by dead control flow\n{}\n        this.mainWindow.once('ready-to-show', () => {\n            this.mainWindow?.show();\n        });\n        this.mainWindow.on('closed', () => {\n            this.mainWindow = null;\n        });\n    }\n    setupMenu() {\n        const template = [\n            {\n                label: 'File',\n                submenu: [\n                    {\n                        label: 'New File',\n                        accelerator: 'CmdOrCtrl+N',\n                        click: () => {\n                            this.mainWindow?.webContents.send('menu-new-file');\n                        },\n                    },\n                    {\n                        label: 'Open File',\n                        accelerator: 'CmdOrCtrl+O',\n                        click: () => {\n                            this.openFile();\n                        },\n                    },\n                    {\n                        label: 'Open Folder',\n                        accelerator: 'CmdOrCtrl+Shift+O',\n                        click: () => {\n                            this.openFolder();\n                        },\n                    },\n                    { type: 'separator' },\n                    {\n                        label: 'Save',\n                        accelerator: 'CmdOrCtrl+S',\n                        click: () => {\n                            this.mainWindow?.webContents.send('menu-save');\n                        },\n                    },\n                    {\n                        label: 'Save As',\n                        accelerator: 'CmdOrCtrl+Shift+S',\n                        click: () => {\n                            this.mainWindow?.webContents.send('menu-save-as');\n                        },\n                    },\n                    { type: 'separator' },\n                    {\n                        label: 'Export as PDF',\n                        accelerator: 'CmdOrCtrl+E',\n                        click: () => {\n                            this.mainWindow?.webContents.send('menu-export-pdf');\n                        },\n                    },\n                    {\n                        label: 'Import Word Document',\n                        click: () => {\n                            this.importWordDocument();\n                        },\n                    },\n                ],\n            },\n            {\n                label: 'Edit',\n                submenu: [\n                    { role: 'undo' },\n                    { role: 'redo' },\n                    { type: 'separator' },\n                    { role: 'cut' },\n                    { role: 'copy' },\n                    { role: 'paste' },\n                    { role: 'selectAll' },\n                ],\n            },\n            {\n                label: 'View',\n                submenu: [\n                    { role: 'reload' },\n                    { role: 'forceReload' },\n                    { role: 'toggleDevTools' },\n                    { type: 'separator' },\n                    { role: 'resetZoom' },\n                    { role: 'zoomIn' },\n                    { role: 'zoomOut' },\n                    { type: 'separator' },\n                    { role: 'togglefullscreen' },\n                ],\n            },\n        ];\n        const menu = electron__WEBPACK_IMPORTED_MODULE_0__.Menu.buildFromTemplate(template);\n        electron__WEBPACK_IMPORTED_MODULE_0__.Menu.setApplicationMenu(menu);\n    }\n    setupIpcHandlers() {\n        // File system operations\n        electron__WEBPACK_IMPORTED_MODULE_0__.ipcMain.handle('read-file', async (_, filePath) => {\n            try {\n                const content = await fs__WEBPACK_IMPORTED_MODULE_2__.promises.readFile(filePath, 'utf-8');\n                return { success: true, content };\n            }\n            catch (error) {\n                return { success: false, error: error.message };\n            }\n        });\n        electron__WEBPACK_IMPORTED_MODULE_0__.ipcMain.handle('write-file', async (_, filePath, content) => {\n            try {\n                await fs__WEBPACK_IMPORTED_MODULE_2__.promises.writeFile(filePath, content, 'utf-8');\n                return { success: true };\n            }\n            catch (error) {\n                return { success: false, error: error.message };\n            }\n        });\n        electron__WEBPACK_IMPORTED_MODULE_0__.ipcMain.handle('read-directory', async (_, dirPath) => {\n            try {\n                const items = await fs__WEBPACK_IMPORTED_MODULE_2__.promises.readdir(dirPath, { withFileTypes: true });\n                const result = items.map(item => ({\n                    name: item.name,\n                    isDirectory: item.isDirectory(),\n                    path: path__WEBPACK_IMPORTED_MODULE_1__.join(dirPath, item.name),\n                }));\n                return { success: true, items: result };\n            }\n            catch (error) {\n                return { success: false, error: error.message };\n            }\n        });\n    }\n    async openFile() {\n        const result = await electron__WEBPACK_IMPORTED_MODULE_0__.dialog.showOpenDialog(this.mainWindow, {\n            properties: ['openFile'],\n            filters: [\n                { name: 'Markdown Files', extensions: ['md', 'markdown', 'mdown', 'mkd'] },\n                { name: 'All Files', extensions: ['*'] },\n            ],\n        });\n        if (!result.canceled && result.filePaths.length > 0) {\n            this.mainWindow?.webContents.send('file-opened', result.filePaths[0]);\n        }\n    }\n    async openFolder() {\n        const result = await electron__WEBPACK_IMPORTED_MODULE_0__.dialog.showOpenDialog(this.mainWindow, {\n            properties: ['openDirectory'],\n        });\n        if (!result.canceled && result.filePaths.length > 0) {\n            this.mainWindow?.webContents.send('folder-opened', result.filePaths[0]);\n        }\n    }\n    async importWordDocument() {\n        const result = await electron__WEBPACK_IMPORTED_MODULE_0__.dialog.showOpenDialog(this.mainWindow, {\n            properties: ['openFile'],\n            filters: [\n                { name: 'Word Documents', extensions: ['docx'] },\n            ],\n        });\n        if (!result.canceled && result.filePaths.length > 0) {\n            this.mainWindow?.webContents.send('word-import', result.filePaths[0]);\n        }\n    }\n}\n// Initialize the application\nnew StdMDApp();\n\n\n//# sourceURL=webpack://stdmd/./src/main/main.ts?");

/***/ }),

/***/ "electron":
/*!***************************!*\
  !*** external "electron" ***!
  \***************************/
/***/ ((module) => {

module.exports = require("electron");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

module.exports = require("fs");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("path");

/***/ })

/******/ 	});
/************************************************************************/
/******/ 	// The module cache
/******/ 	var __webpack_module_cache__ = {};
/******/ 	
/******/ 	// The require function
/******/ 	function __webpack_require__(moduleId) {
/******/ 		// Check if module is in cache
/******/ 		var cachedModule = __webpack_module_cache__[moduleId];
/******/ 		if (cachedModule !== undefined) {
/******/ 			return cachedModule.exports;
/******/ 		}
/******/ 		// Create a new module (and put it into the cache)
/******/ 		var module = __webpack_module_cache__[moduleId] = {
/******/ 			// no module.id needed
/******/ 			// no module.loaded needed
/******/ 			exports: {}
/******/ 		};
/******/ 	
/******/ 		// Execute the module function
/******/ 		__webpack_modules__[moduleId](module, module.exports, __webpack_require__);
/******/ 	
/******/ 		// Return the exports of the module
/******/ 		return module.exports;
/******/ 	}
/******/ 	
/************************************************************************/
/******/ 	/* webpack/runtime/compat get default export */
/******/ 	(() => {
/******/ 		// getDefaultExport function for compatibility with non-harmony modules
/******/ 		__webpack_require__.n = (module) => {
/******/ 			var getter = module && module.__esModule ?
/******/ 				() => (module['default']) :
/******/ 				() => (module);
/******/ 			__webpack_require__.d(getter, { a: getter });
/******/ 			return getter;
/******/ 		};
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/define property getters */
/******/ 	(() => {
/******/ 		// define getter functions for harmony exports
/******/ 		__webpack_require__.d = (exports, definition) => {
/******/ 			for(var key in definition) {
/******/ 				if(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {
/******/ 					Object.defineProperty(exports, key, { enumerable: true, get: definition[key] });
/******/ 				}
/******/ 			}
/******/ 		};
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/hasOwnProperty shorthand */
/******/ 	(() => {
/******/ 		__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/make namespace object */
/******/ 	(() => {
/******/ 		// define __esModule on exports
/******/ 		__webpack_require__.r = (exports) => {
/******/ 			if(typeof Symbol !== 'undefined' && Symbol.toStringTag) {
/******/ 				Object.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });
/******/ 			}
/******/ 			Object.defineProperty(exports, '__esModule', { value: true });
/******/ 		};
/******/ 	})();
/******/ 	
/************************************************************************/
/******/ 	
/******/ 	// startup
/******/ 	// Load entry module and return exports
/******/ 	// This entry module can't be inlined because the eval devtool is used.
/******/ 	var __webpack_exports__ = __webpack_require__("./src/main/main.ts");
/******/ 	
/******/ })()
;