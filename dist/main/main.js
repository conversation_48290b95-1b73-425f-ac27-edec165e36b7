(()=>{"use strict";const e=require("electron"),t=require("path"),n=require("fs");new class{constructor(){this.mainWindow=null,this.initializeApp()}initializeApp(){e.app.whenReady().then(()=>{this.createMainWindow(),this.setupMenu(),this.setupIpcHandlers()}),e.app.on("window-all-closed",()=>{"darwin"!==process.platform&&e.app.quit()}),e.app.on("activate",()=>{0===e.BrowserWindow.getAllWindows().length&&this.createMainWindow()})}createMainWindow(){this.mainWindow=new e.BrowserWindow({width:1200,height:800,minWidth:800,minHeight:600,webPreferences:{nodeIntegration:!1,contextIsolation:!0,preload:t.join(__dirname,"preload.js")},titleBarStyle:"hiddenInset",show:!1}),this.mainWindow.loadFile(t.join(__dirname,"../renderer/index.html")),this.mainWindow.once("ready-to-show",()=>{this.mainWindow?.show()}),this.mainWindow.on("closed",()=>{this.mainWindow=null})}setupMenu(){const t=[{label:"File",submenu:[{label:"New File",accelerator:"CmdOrCtrl+N",click:()=>{this.mainWindow?.webContents.send("menu-new-file")}},{label:"Open File",accelerator:"CmdOrCtrl+O",click:()=>{this.openFile()}},{label:"Open Folder",accelerator:"CmdOrCtrl+Shift+O",click:()=>{this.openFolder()}},{type:"separator"},{label:"Save",accelerator:"CmdOrCtrl+S",click:()=>{this.mainWindow?.webContents.send("menu-save")}},{label:"Save As",accelerator:"CmdOrCtrl+Shift+S",click:()=>{this.mainWindow?.webContents.send("menu-save-as")}},{type:"separator"},{label:"Export as PDF",accelerator:"CmdOrCtrl+E",click:()=>{this.mainWindow?.webContents.send("menu-export-pdf")}},{label:"Import Word Document",click:()=>{this.importWordDocument()}}]},{label:"Edit",submenu:[{role:"undo"},{role:"redo"},{type:"separator"},{role:"cut"},{role:"copy"},{role:"paste"},{role:"selectAll"}]},{label:"View",submenu:[{role:"reload"},{role:"forceReload"},{role:"toggleDevTools"},{type:"separator"},{role:"resetZoom"},{role:"zoomIn"},{role:"zoomOut"},{type:"separator"},{role:"togglefullscreen"}]}],n=e.Menu.buildFromTemplate(t);e.Menu.setApplicationMenu(n)}setupIpcHandlers(){e.ipcMain.handle("read-file",async(e,t)=>{try{return{success:!0,content:await n.promises.readFile(t,"utf-8")}}catch(e){return{success:!1,error:e.message}}}),e.ipcMain.handle("read-file-binary",async(e,t)=>{try{return{success:!0,content:(await n.promises.readFile(t)).toString("base64")}}catch(e){return{success:!1,error:e.message}}}),e.ipcMain.handle("write-file",async(e,t,i)=>{try{return await n.promises.writeFile(t,i,"utf-8"),{success:!0}}catch(e){return{success:!1,error:e.message}}}),e.ipcMain.handle("read-directory",async(e,i)=>{try{return{success:!0,items:(await n.promises.readdir(i,{withFileTypes:!0})).map(e=>({name:e.name,isDirectory:e.isDirectory(),path:t.join(i,e.name)}))}}catch(e){return{success:!1,error:e.message}}}),e.ipcMain.handle("generate-pdf",async(t,i,a,o)=>{try{const t=await e.dialog.showSaveDialog(this.mainWindow,{defaultPath:a,filters:[{name:"PDF Files",extensions:["pdf"]}]});if(t.canceled||!t.filePath)return{success:!1,error:"Save canceled"};const r=new e.BrowserWindow({width:800,height:600,show:!1,webPreferences:{nodeIntegration:!1,contextIsolation:!0}});await r.loadURL(`data:text/html;charset=utf-8,${encodeURIComponent(i)}`);const s=await r.webContents.printToPDF({pageSize:o.format||"A4",landscape:"landscape"===o.orientation,margins:{top:o.margins?.top||"1in",bottom:o.margins?.bottom||"1in",left:o.margins?.left||"1in",right:o.margins?.right||"1in"},printBackground:!0,displayHeaderFooter:o.headerFooter?.displayHeaderFooter||!1,headerTemplate:o.headerFooter?.headerTemplate||"",footerTemplate:o.headerFooter?.footerTemplate||""});return await n.promises.writeFile(t.filePath,s),r.close(),{success:!0,filePath:t.filePath}}catch(e){return{success:!1,error:e.message}}})}async openFile(){const t=await e.dialog.showOpenDialog(this.mainWindow,{properties:["openFile"],filters:[{name:"Markdown Files",extensions:["md","markdown","mdown","mkd"]},{name:"All Files",extensions:["*"]}]});!t.canceled&&t.filePaths.length>0&&(console.log("Main: Opening file:",t.filePaths[0]),this.mainWindow?.webContents.send("file-opened",t.filePaths[0]))}async openFolder(){const t=await e.dialog.showOpenDialog(this.mainWindow,{properties:["openDirectory"]});!t.canceled&&t.filePaths.length>0&&(console.log("Main: Opening folder:",t.filePaths[0]),this.mainWindow?.webContents.send("folder-opened",t.filePaths[0]))}async importWordDocument(){const t=await e.dialog.showOpenDialog(this.mainWindow,{properties:["openFile"],filters:[{name:"Word Documents",extensions:["docx"]}]});!t.canceled&&t.filePaths.length>0&&this.mainWindow?.webContents.send("word-import",t.filePaths[0])}}})();