# Markdown基础语法示例

这个文件展示了Markdown的基础语法和在StdMD中的渲染效果。

## 标题

# 一级标题
## 二级标题
### 三级标题
#### 四级标题
##### 五级标题
###### 六级标题

## 文本格式

**这是粗体文本**

*这是斜体文本*

***这是粗斜体文本***

~~这是删除线文本~~

这是包含`行内代码`的段落。

## 列表

### 无序列表
- 项目 1
- 项目 2
  - 子项目 2.1
  - 子项目 2.2
- 项目 3

### 有序列表
1. 第一项
2. 第二项
   1. 子项目 2.1
   2. 子项目 2.2
3. 第三项

### 任务列表
- [x] 已完成的任务
- [ ] 未完成的任务
- [x] 另一个已完成的任务

## 链接和图片

[这是一个链接](https://www.example.com)

[这是一个带标题的链接](https://www.example.com "链接标题")

![图片替代文本](https://via.placeholder.com/300x200 "图片标题")

## 引用

> 这是一个引用块。
> 
> 引用可以包含多个段落。
> 
> > 这是嵌套引用。

## 代码

### 行内代码
使用 `console.log()` 输出信息。

### 代码块

```javascript
function greet(name) {
    console.log(`Hello, ${name}!`);
}

greet("World");
```

```python
def fibonacci(n):
    if n <= 1:
        return n
    return fibonacci(n-1) + fibonacci(n-2)

print(fibonacci(10))
```

```bash
# 安装依赖
npm install

# 启动开发服务器
npm run dev

# 构建项目
npm run build
```

## 表格

| 功能 | 支持状态 | 备注 |
|------|----------|------|
| 基础语法 | ✅ | 完全支持 |
| 扩展语法 | ✅ | GitHub风格 |
| 数学公式 | ✅ | LaTeX语法 |
| 图表 | 🚧 | 开发中 |

| 左对齐 | 居中对齐 | 右对齐 |
|:-------|:--------:|-------:|
| 内容1  |   内容2   |  内容3 |
| 长内容 |   短内容   |  中等内容 |

## 分割线

---

***

___

## 转义字符

使用反斜杠转义特殊字符：

\*这不是斜体\*

\`这不是代码\`

\[这不是链接\]

## 脚注

这是一个带脚注的句子[^1]。

这是另一个脚注[^note]。

[^1]: 这是第一个脚注的内容。

[^note]: 这是命名脚注的内容，可以包含多行。
    
    甚至可以包含代码块：
    
    ```
    console.log("脚注中的代码");
    ```

## 数学公式

行内公式：$E = mc^2$

块级公式：

$$
\int_{-\infty}^{\infty} e^{-x^2} dx = \sqrt{\pi}
$$

$$
\begin{align}
f(x) &= ax^2 + bx + c \\
g(x) &= dx + e
\end{align}
$$

## HTML标签

Markdown也支持一些HTML标签：

<kbd>Ctrl</kbd> + <kbd>C</kbd>

<mark>高亮文本</mark>

<details>
<summary>点击展开</summary>

这是隐藏的内容。

</details>

## 总结

这个文件展示了Markdown的主要语法特性。在StdMD中，所有这些语法都能得到很好的支持和渲染。
