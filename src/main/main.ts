import { app, BrowserWindow, Menu, ipc<PERSON>ain, dialog } from 'electron';
import * as path from 'path';
import * as fs from 'fs';

class StdMDApp {
  private mainWindow: BrowserWindow | null = null;

  constructor() {
    this.initializeApp();
  }

  private initializeApp(): void {
    app.whenReady().then(() => {
      this.createMainWindow();
      this.setupMenu();
      this.setupIpcHandlers();
    });

    app.on('window-all-closed', () => {
      if (process.platform !== 'darwin') {
        app.quit();
      }
    });

    app.on('activate', () => {
      if (BrowserWindow.getAllWindows().length === 0) {
        this.createMainWindow();
      }
    });
  }

  private createMainWindow(): void {
    this.mainWindow = new BrowserWindow({
      width: 1200,
      height: 800,
      minWidth: 800,
      minHeight: 600,
      webPreferences: {
        nodeIntegration: false,
        contextIsolation: true,
        preload: path.join(__dirname, 'preload.js'),
      },
      titleBarStyle: 'hiddenInset',
      show: false,
    });

    // Load the app
    if (process.env.NODE_ENV === 'development') {
      this.mainWindow.loadURL('http://localhost:3000');
      this.mainWindow.webContents.openDevTools();
    } else {
      this.mainWindow.loadFile(path.join(__dirname, '../renderer/index.html'));
    }

    this.mainWindow.once('ready-to-show', () => {
      this.mainWindow?.show();
    });

    this.mainWindow.on('closed', () => {
      this.mainWindow = null;
    });
  }

  private setupMenu(): void {
    const template: Electron.MenuItemConstructorOptions[] = [
      {
        label: 'File',
        submenu: [
          {
            label: 'New File',
            accelerator: 'CmdOrCtrl+N',
            click: () => {
              this.mainWindow?.webContents.send('menu-new-file');
            },
          },
          {
            label: 'Open File',
            accelerator: 'CmdOrCtrl+O',
            click: () => {
              this.openFile();
            },
          },
          {
            label: 'Open Folder',
            accelerator: 'CmdOrCtrl+Shift+O',
            click: () => {
              this.openFolder();
            },
          },
          { type: 'separator' },
          {
            label: 'Save',
            accelerator: 'CmdOrCtrl+S',
            click: () => {
              this.mainWindow?.webContents.send('menu-save');
            },
          },
          {
            label: 'Save As',
            accelerator: 'CmdOrCtrl+Shift+S',
            click: () => {
              this.mainWindow?.webContents.send('menu-save-as');
            },
          },
          { type: 'separator' },
          {
            label: 'Export as PDF',
            accelerator: 'CmdOrCtrl+E',
            click: () => {
              this.mainWindow?.webContents.send('menu-export-pdf');
            },
          },
          {
            label: 'Import Word Document',
            click: () => {
              this.importWordDocument();
            },
          },
        ],
      },
      {
        label: 'Edit',
        submenu: [
          { role: 'undo' },
          { role: 'redo' },
          { type: 'separator' },
          { role: 'cut' },
          { role: 'copy' },
          { role: 'paste' },
          { role: 'selectAll' },
        ],
      },
      {
        label: 'View',
        submenu: [
          { role: 'reload' },
          { role: 'forceReload' },
          { role: 'toggleDevTools' },
          { type: 'separator' },
          { role: 'resetZoom' },
          { role: 'zoomIn' },
          { role: 'zoomOut' },
          { type: 'separator' },
          { role: 'togglefullscreen' },
        ],
      },
    ];

    const menu = Menu.buildFromTemplate(template);
    Menu.setApplicationMenu(menu);
  }

  private setupIpcHandlers(): void {
    // File system operations
    ipcMain.handle('read-file', async (_, filePath: string) => {
      try {
        const content = await fs.promises.readFile(filePath, 'utf-8');
        return { success: true, content };
      } catch (error) {
        return { success: false, error: (error as Error).message };
      }
    });

    ipcMain.handle('read-file-binary', async (_, filePath: string) => {
      try {
        const buffer = await fs.promises.readFile(filePath);
        const base64 = buffer.toString('base64');
        return { success: true, content: base64 };
      } catch (error) {
        return { success: false, error: (error as Error).message };
      }
    });

    ipcMain.handle('write-file', async (_, filePath: string, content: string) => {
      try {
        await fs.promises.writeFile(filePath, content, 'utf-8');
        return { success: true };
      } catch (error) {
        return { success: false, error: (error as Error).message };
      }
    });

    ipcMain.handle('read-directory', async (_, dirPath: string) => {
      try {
        const items = await fs.promises.readdir(dirPath, { withFileTypes: true });
        const result = items.map(item => ({
          name: item.name,
          isDirectory: item.isDirectory(),
          path: path.join(dirPath, item.name),
        }));
        return { success: true, items: result };
      } catch (error) {
        return { success: false, error: (error as Error).message };
      }
    });

    // PDF generation
    ipcMain.handle('generate-pdf', async (_, htmlContent: string, filename: string, options: any) => {
      try {
        const result = await dialog.showSaveDialog(this.mainWindow!, {
          defaultPath: filename,
          filters: [
            { name: 'PDF Files', extensions: ['pdf'] },
          ],
        });

        if (result.canceled || !result.filePath) {
          return { success: false, error: 'Save canceled' };
        }

        // Create a new BrowserWindow for PDF generation
        const pdfWindow = new BrowserWindow({
          width: 800,
          height: 600,
          show: false,
          webPreferences: {
            nodeIntegration: false,
            contextIsolation: true,
          },
        });

        // Load the HTML content
        await pdfWindow.loadURL(`data:text/html;charset=utf-8,${encodeURIComponent(htmlContent)}`);

        // Generate PDF
        const pdfBuffer = await pdfWindow.webContents.printToPDF({
          pageSize: options.format || 'A4',
          landscape: options.orientation === 'landscape',
          margins: {
            top: options.margins?.top || '1in',
            bottom: options.margins?.bottom || '1in',
            left: options.margins?.left || '1in',
            right: options.margins?.right || '1in',
          },
          printBackground: true,
          displayHeaderFooter: options.headerFooter?.displayHeaderFooter || false,
          headerTemplate: options.headerFooter?.headerTemplate || '',
          footerTemplate: options.headerFooter?.footerTemplate || '',
        });

        // Save the PDF
        await fs.promises.writeFile(result.filePath, pdfBuffer);

        // Close the PDF window
        pdfWindow.close();

        return { success: true, filePath: result.filePath };
      } catch (error) {
        return { success: false, error: (error as Error).message };
      }
    });
  }

  private async openFile(): Promise<void> {
    const result = await dialog.showOpenDialog(this.mainWindow!, {
      properties: ['openFile'],
      filters: [
        { name: 'Markdown Files', extensions: ['md', 'markdown', 'mdown', 'mkd'] },
        { name: 'All Files', extensions: ['*'] },
      ],
    });

    if (!result.canceled && result.filePaths.length > 0) {
      console.log('Main: Opening file:', result.filePaths[0]);
      this.mainWindow?.webContents.send('file-opened', result.filePaths[0]);
    }
  }

  private async openFolder(): Promise<void> {
    const result = await dialog.showOpenDialog(this.mainWindow!, {
      properties: ['openDirectory'],
    });

    if (!result.canceled && result.filePaths.length > 0) {
      console.log('Main: Opening folder:', result.filePaths[0]);
      this.mainWindow?.webContents.send('folder-opened', result.filePaths[0]);
    }
  }

  private async importWordDocument(): Promise<void> {
    const result = await dialog.showOpenDialog(this.mainWindow!, {
      properties: ['openFile'],
      filters: [
        { name: 'Word Documents', extensions: ['docx'] },
      ],
    });

    if (!result.canceled && result.filePaths.length > 0) {
      this.mainWindow?.webContents.send('word-import', result.filePaths[0]);
    }
  }
}

// Initialize the application
new StdMDApp();
