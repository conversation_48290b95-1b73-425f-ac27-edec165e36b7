import { contextBridge, ipc<PERSON>enderer } from 'electron';

// Expose protected methods that allow the renderer process to use
// the ipcRenderer without exposing the entire object
contextBridge.exposeInMainWorld('electronAPI', {
  // File operations
  readFile: (filePath: string) => ipcRenderer.invoke('read-file', filePath),
  writeFile: (filePath: string, content: string) => ipcRenderer.invoke('write-file', filePath, content),
  readDirectory: (dirPath: string) => ipcRenderer.invoke('read-directory', dirPath),

  // PDF generation
  generatePDF: (htmlContent: string, filename: string, options: any) =>
    ipcRenderer.invoke('generate-pdf', htmlContent, filename, options),

  // Menu events
  onMenuNewFile: (callback: () => void) => ipcRenderer.on('menu-new-file', callback),
  onMenuSave: (callback: () => void) => ipcRenderer.on('menu-save', callback),
  onMenuSaveAs: (callback: () => void) => ipcRenderer.on('menu-save-as', callback),
  onMenuExportPdf: (callback: () => void) => ipcRenderer.on('menu-export-pdf', callback),

  // File/folder events
  onFileOpened: (callback: (filePath: string) => void) => 
    ipcRenderer.on('file-opened', (_, filePath) => callback(filePath)),
  onFolderOpened: (callback: (folderPath: string) => void) => 
    ipcRenderer.on('folder-opened', (_, folderPath) => callback(folderPath)),
  onWordImport: (callback: (filePath: string) => void) => 
    ipcRenderer.on('word-import', (_, filePath) => callback(filePath)),

  // Remove listeners
  removeAllListeners: (channel: string) => ipcRenderer.removeAllListeners(channel),
});

// Type definitions for the exposed API
declare global {
  interface Window {
    electronAPI: {
      readFile: (filePath: string) => Promise<{ success: boolean; content?: string; error?: string }>;
      writeFile: (filePath: string, content: string) => Promise<{ success: boolean; error?: string }>;
      readDirectory: (dirPath: string) => Promise<{
        success: boolean;
        items?: Array<{ name: string; isDirectory: boolean; path: string }>;
        error?: string
      }>;
      generatePDF: (htmlContent: string, filename: string, options: any) => Promise<{
        success: boolean;
        filePath?: string;
        error?: string;
      }>;
      onMenuNewFile: (callback: () => void) => void;
      onMenuSave: (callback: () => void) => void;
      onMenuSaveAs: (callback: () => void) => void;
      onMenuExportPdf: (callback: () => void) => void;
      onFileOpened: (callback: (filePath: string) => void) => void;
      onFolderOpened: (callback: (folderPath: string) => void) => void;
      onWordImport: (callback: (filePath: string) => void) => void;
      removeAllListeners: (channel: string) => void;
    };
  }
}
