import React, { useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import Layout from './components/Layout/Layout';
import LayoutTest from './components/Layout/LayoutTest';
import { useAppStore } from './stores/appStore';
import './styles/App.css';

const App: React.FC = () => {
  const { initializeApp } = useAppStore();

  useEffect(() => {
    initializeApp();
  }, [initializeApp]);

  return (
    <Router>
      <div className="app">
        <Routes>
          <Route path="/" element={<Layout />} />
        </Routes>
      </div>
    </Router>
  );
};

export default App;
