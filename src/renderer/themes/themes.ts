export interface Theme {
  id: string;
  name: string;
  description: string;
  colors: {
    primary: string;
    secondary: string;
    background: string;
    surface: string;
    text: string;
    textSecondary: string;
    border: string;
    accent: string;
    success: string;
    warning: string;
    error: string;
  };
  editor: {
    background: string;
    text: string;
    selection: string;
    lineNumber: string;
    cursor: string;
  };
  preview: {
    background: string;
    text: string;
    codeBackground: string;
    blockquoteBackground: string;
    tableHeaderBackground: string;
  };
}

export const lightTheme: Theme = {
  id: 'light',
  name: 'Light',
  description: 'Clean and bright theme for daytime use',
  colors: {
    primary: '#0969da',
    secondary: '#656d76',
    background: '#ffffff',
    surface: '#f6f8fa',
    text: '#24292e',
    textSecondary: '#656d76',
    border: '#e1e4e8',
    accent: '#0969da',
    success: '#28a745',
    warning: '#ffc107',
    error: '#dc3545',
  },
  editor: {
    background: '#ffffff',
    text: '#24292e',
    selection: '#b3d4fc',
    lineNumber: '#656d76',
    cursor: '#24292e',
  },
  preview: {
    background: '#ffffff',
    text: '#24292e',
    codeBackground: '#f6f8fa',
    blockquoteBackground: '#f6f8fa',
    tableHeaderBackground: '#f1f3f4',
  },
};

export const darkTheme: Theme = {
  id: 'dark',
  name: 'Dark',
  description: 'Easy on the eyes for nighttime coding',
  colors: {
    primary: '#58a6ff',
    secondary: '#8b949e',
    background: '#0d1117',
    surface: '#161b22',
    text: '#f0f6fc',
    textSecondary: '#8b949e',
    border: '#30363d',
    accent: '#58a6ff',
    success: '#3fb950',
    warning: '#d29922',
    error: '#f85149',
  },
  editor: {
    background: '#0d1117',
    text: '#f0f6fc',
    selection: '#264f78',
    lineNumber: '#8b949e',
    cursor: '#f0f6fc',
  },
  preview: {
    background: '#0d1117',
    text: '#f0f6fc',
    codeBackground: '#161b22',
    blockquoteBackground: '#161b22',
    tableHeaderBackground: '#21262d',
  },
};

export const blueTheme: Theme = {
  id: 'blue',
  name: 'Ocean Blue',
  description: 'Calming blue tones for focused writing',
  colors: {
    primary: '#0066cc',
    secondary: '#6c757d',
    background: '#f8f9fa',
    surface: '#e3f2fd',
    text: '#212529',
    textSecondary: '#6c757d',
    border: '#bbdefb',
    accent: '#0066cc',
    success: '#28a745',
    warning: '#ffc107',
    error: '#dc3545',
  },
  editor: {
    background: '#f8f9fa',
    text: '#212529',
    selection: '#bbdefb',
    lineNumber: '#6c757d',
    cursor: '#212529',
  },
  preview: {
    background: '#f8f9fa',
    text: '#212529',
    codeBackground: '#e3f2fd',
    blockquoteBackground: '#e3f2fd',
    tableHeaderBackground: '#bbdefb',
  },
};

export const greenTheme: Theme = {
  id: 'green',
  name: 'Forest Green',
  description: 'Natural green theme for a refreshing experience',
  colors: {
    primary: '#28a745',
    secondary: '#6c757d',
    background: '#f8f9fa',
    surface: '#e8f5e8',
    text: '#212529',
    textSecondary: '#6c757d',
    border: '#c3e6cb',
    accent: '#28a745',
    success: '#28a745',
    warning: '#ffc107',
    error: '#dc3545',
  },
  editor: {
    background: '#f8f9fa',
    text: '#212529',
    selection: '#c3e6cb',
    lineNumber: '#6c757d',
    cursor: '#212529',
  },
  preview: {
    background: '#f8f9fa',
    text: '#212529',
    codeBackground: '#e8f5e8',
    blockquoteBackground: '#e8f5e8',
    tableHeaderBackground: '#c3e6cb',
  },
};

export const purpleTheme: Theme = {
  id: 'purple',
  name: 'Royal Purple',
  description: 'Elegant purple theme for creative writing',
  colors: {
    primary: '#6f42c1',
    secondary: '#6c757d',
    background: '#f8f9fa',
    surface: '#f3e5f5',
    text: '#212529',
    textSecondary: '#6c757d',
    border: '#d1c4e9',
    accent: '#6f42c1',
    success: '#28a745',
    warning: '#ffc107',
    error: '#dc3545',
  },
  editor: {
    background: '#f8f9fa',
    text: '#212529',
    selection: '#d1c4e9',
    lineNumber: '#6c757d',
    cursor: '#212529',
  },
  preview: {
    background: '#f8f9fa',
    text: '#212529',
    codeBackground: '#f3e5f5',
    blockquoteBackground: '#f3e5f5',
    tableHeaderBackground: '#d1c4e9',
  },
};

export const themes: Theme[] = [
  lightTheme,
  darkTheme,
  blueTheme,
  greenTheme,
  purpleTheme,
];

export const getThemeById = (id: string): Theme => {
  return themes.find(theme => theme.id === id) || lightTheme;
};

export const applyTheme = (theme: Theme): void => {
  const root = document.documentElement;
  
  // Apply CSS custom properties
  Object.entries(theme.colors).forEach(([key, value]) => {
    root.style.setProperty(`--color-${key}`, value);
  });
  
  Object.entries(theme.editor).forEach(([key, value]) => {
    root.style.setProperty(`--editor-${key}`, value);
  });
  
  Object.entries(theme.preview).forEach(([key, value]) => {
    root.style.setProperty(`--preview-${key}`, value);
  });
  
  // Update body class for theme-specific styles
  document.body.className = document.body.className.replace(/theme-\w+/g, '');
  document.body.classList.add(`theme-${theme.id}`);
};
