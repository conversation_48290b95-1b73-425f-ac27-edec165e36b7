import { create } from 'zustand';
import { subscribeWithSelector } from 'zustand/middleware';
import { applyTheme, getThemeById } from '../themes/themes';

export interface FileItem {
  name: string;
  path: string;
  isDirectory: boolean;
  content?: string;
  isModified?: boolean;
  children?: FileItem[];
  size?: number;
  lastModified?: Date;
}

export interface AppState {
  // File management
  currentWorkspace: string | null;
  openFiles: FileItem[];
  activeFileIndex: number;
  fileTree: FileItem[];

  // Editor state
  editorContent: string;
  isPreviewMode: boolean;
  currentTheme: string;

  // UI state
  sidebarVisible: boolean;
  previewVisible: boolean;
  themeDialogOpen: boolean;

  // Actions
  initializeApp: () => void;
  setWorkspace: (path: string) => void;
  openFile: (file: FileItem) => void;
  closeFile: (index: number) => void;
  setActiveFile: (index: number) => void;
  updateFileContent: (content: string) => void;
  togglePreview: () => void;
  toggleSidebar: () => void;
  setTheme: (theme: string) => void;
  openThemeDialog: () => void;
  closeThemeDialog: () => void;
  loadFileTree: (path: string) => Promise<void>;
}

export const useAppStore = create<AppState>()(
  subscribeWithSelector((set, get) => ({
    // Initial state
    currentWorkspace: null,
    openFiles: [],
    activeFileIndex: -1,
    fileTree: [],
    editorContent: '',
    isPreviewMode: false,
    currentTheme: 'light',
    sidebarVisible: true,
    previewVisible: true,
    themeDialogOpen: false,

    // Actions
    initializeApp: () => {
      // Apply initial theme
      const { currentTheme } = get();
      const theme = getThemeById(currentTheme);
      applyTheme(theme);

      // Setup electron event listeners
      if (window.electronAPI) {
        window.electronAPI.onFileOpened((filePath: string) => {
          const { openFile } = get();
          openFile({
            name: filePath.split('/').pop() || 'Untitled',
            path: filePath,
            isDirectory: false,
          });
        });

        window.electronAPI.onFolderOpened((folderPath: string) => {
          const { setWorkspace } = get();
          setWorkspace(folderPath);
        });

        window.electronAPI.onMenuNewFile(() => {
          const { openFile } = get();
          openFile({
            name: 'Untitled.md',
            path: '',
            isDirectory: false,
            content: '',
          });
        });

        window.electronAPI.onMenuSave(() => {
          // TODO: Implement save functionality
          console.log('Save triggered');
        });

        window.electronAPI.onMenuSaveAs(() => {
          // TODO: Implement save as functionality
          console.log('Save As triggered');
        });

        window.electronAPI.onMenuExportPdf(() => {
          // TODO: Implement PDF export
          console.log('Export PDF triggered');
        });

        window.electronAPI.onWordImport((filePath: string) => {
          // TODO: Implement Word import
          console.log('Word import triggered:', filePath);
        });
      }
    },

    setWorkspace: async (path: string) => {
      set({ currentWorkspace: path });
      await get().loadFileTree(path);
    },

    openFile: async (file: FileItem) => {
      const { openFiles } = get();
      
      // Check if file is already open
      const existingIndex = openFiles.findIndex(f => f.path === file.path);
      if (existingIndex !== -1) {
        set({ activeFileIndex: existingIndex });
        return;
      }

      // Load file content if it's a file and has a path
      let content = file.content || '';
      if (!file.isDirectory && file.path && window.electronAPI) {
        const result = await window.electronAPI.readFile(file.path);
        if (result.success) {
          content = result.content || '';
        }
      }

      const newFile = { ...file, content };
      const newOpenFiles = [...openFiles, newFile];
      
      set({
        openFiles: newOpenFiles,
        activeFileIndex: newOpenFiles.length - 1,
        editorContent: content,
      });
    },

    closeFile: (index: number) => {
      const { openFiles, activeFileIndex } = get();
      const newOpenFiles = openFiles.filter((_, i) => i !== index);
      
      let newActiveIndex = activeFileIndex;
      if (index === activeFileIndex) {
        newActiveIndex = Math.min(activeFileIndex, newOpenFiles.length - 1);
      } else if (index < activeFileIndex) {
        newActiveIndex = activeFileIndex - 1;
      }

      const newContent = newActiveIndex >= 0 ? newOpenFiles[newActiveIndex]?.content || '' : '';

      set({
        openFiles: newOpenFiles,
        activeFileIndex: newActiveIndex,
        editorContent: newContent,
      });
    },

    setActiveFile: (index: number) => {
      const { openFiles } = get();
      if (index >= 0 && index < openFiles.length) {
        set({
          activeFileIndex: index,
          editorContent: openFiles[index].content || '',
        });
      }
    },

    updateFileContent: (content: string) => {
      const { openFiles, activeFileIndex } = get();
      if (activeFileIndex >= 0 && activeFileIndex < openFiles.length) {
        const newOpenFiles = [...openFiles];
        newOpenFiles[activeFileIndex] = {
          ...newOpenFiles[activeFileIndex],
          content,
          isModified: true,
        };
        set({
          openFiles: newOpenFiles,
          editorContent: content,
        });
      }
    },

    togglePreview: () => {
      set(state => ({ previewVisible: !state.previewVisible }));
    },

    toggleSidebar: () => {
      set(state => ({ sidebarVisible: !state.sidebarVisible }));
    },

    setTheme: (theme: string) => {
      set({ currentTheme: theme });
      const themeObj = getThemeById(theme);
      applyTheme(themeObj);
    },

    openThemeDialog: () => {
      set({ themeDialogOpen: true });
    },

    closeThemeDialog: () => {
      set({ themeDialogOpen: false });
    },

    loadFileTree: async (path: string) => {
      if (!window.electronAPI) return;

      const result = await window.electronAPI.readDirectory(path);
      if (result.success && result.items) {
        const sortedItems = result.items.sort((a, b) => {
          // Directories first, then files
          if (a.isDirectory && !b.isDirectory) return -1;
          if (!a.isDirectory && b.isDirectory) return 1;
          return a.name.localeCompare(b.name);
        });

        // If this is the root directory, set as fileTree
        const { currentWorkspace } = get();
        if (path === currentWorkspace) {
          set({ fileTree: sortedItems });
        } else {
          // Update nested directory in the tree
          const updateNestedDirectory = (items: FileItem[], targetPath: string, newItems: any[]): FileItem[] => {
            return items.map(item => {
              if (item.path === targetPath && item.isDirectory) {
                return { ...item, children: newItems };
              } else if (item.isDirectory && item.children) {
                return { ...item, children: updateNestedDirectory(item.children, targetPath, newItems) };
              }
              return item;
            });
          };

          const { fileTree } = get();
          const updatedTree = updateNestedDirectory(fileTree, path, sortedItems);
          set({ fileTree: updatedTree });
        }
      }
    },
  }))
);
