/* CSS Custom Properties (Variables) */
:root {
  /* Default light theme colors */
  --color-primary: #0969da;
  --color-secondary: #656d76;
  --color-background: #ffffff;
  --color-surface: #f6f8fa;
  --color-text: #24292e;
  --color-textSecondary: #656d76;
  --color-border: #e1e4e8;
  --color-accent: #0969da;
  --color-success: #28a745;
  --color-warning: #ffc107;
  --color-error: #dc3545;

  /* Editor colors */
  --editor-background: #ffffff;
  --editor-text: #24292e;
  --editor-selection: #b3d4fc;
  --editor-lineNumber: #656d76;
  --editor-cursor: #24292e;

  /* Preview colors */
  --preview-background: #ffffff;
  --preview-text: #24292e;
  --preview-codeBackground: #f6f8fa;
  --preview-blockquoteBackground: #f6f8fa;
  --preview-tableHeaderBackground: #f1f3f4;
}

/* Global styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: var(--color-background);
  color: var(--color-text);
  overflow: hidden;
  transition: background-color 0.2s ease, color 0.2s ease;
  margin: 0;
  padding: 0;
}

#root {
  height: 100vh;
  width: 100vw;
}

/* Scrollbar styles */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--color-surface);
}

::-webkit-scrollbar-thumb {
  background: var(--color-border);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--color-textSecondary);
}

/* Button styles */
button {
  border: none;
  background: none;
  cursor: pointer;
  font-family: inherit;
}

button:focus {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
}

/* Input styles */
input, textarea {
  font-family: inherit;
  border: 1px solid var(--color-border);
  border-radius: 4px;
  padding: 8px;
  background-color: var(--color-background);
  color: var(--color-text);
}

input:focus, textarea:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: 0 0 0 2px rgba(9, 105, 218, 0.2);
}

/* Utility classes */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}
