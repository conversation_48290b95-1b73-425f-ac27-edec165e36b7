import { marked } from 'marked';

export interface PDFExportOptions {
  title?: string;
  author?: string;
  subject?: string;
  keywords?: string;
  margins?: {
    top: string;
    right: string;
    bottom: string;
    left: string;
  };
  format?: 'A4' | 'Letter' | 'Legal';
  orientation?: 'portrait' | 'landscape';
  includeTableOfContents?: boolean;
  headerFooter?: {
    displayHeaderFooter: boolean;
    headerTemplate?: string;
    footerTemplate?: string;
  };
}

export class PDFExportService {
  private static readonly DEFAULT_OPTIONS: PDFExportOptions = {
    title: 'Markdown Document',
    author: 'StdMD',
    margins: {
      top: '1in',
      right: '1in',
      bottom: '1in',
      left: '1in',
    },
    format: 'A4',
    orientation: 'portrait',
    includeTableOfContents: false,
    headerFooter: {
      displayHeaderFooter: false,
    },
  };

  private static readonly PDF_STYLES = `
    <style>
      @page {
        margin: 1in;
        size: A4;
      }
      
      body {
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
          'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
          sans-serif;
        line-height: 1.6;
        color: #333;
        max-width: none;
        margin: 0;
        padding: 0;
      }
      
      h1, h2, h3, h4, h5, h6 {
        margin-top: 24px;
        margin-bottom: 16px;
        font-weight: 600;
        line-height: 1.25;
        page-break-after: avoid;
      }
      
      h1 {
        font-size: 2em;
        border-bottom: 1px solid #eaecef;
        padding-bottom: 0.3em;
      }
      
      h2 {
        font-size: 1.5em;
        border-bottom: 1px solid #eaecef;
        padding-bottom: 0.3em;
      }
      
      h3 {
        font-size: 1.25em;
      }
      
      p {
        margin-bottom: 16px;
        orphans: 3;
        widows: 3;
      }
      
      blockquote {
        padding: 0 1em;
        color: #6a737d;
        border-left: 0.25em solid #dfe2e5;
        margin: 0 0 16px 0;
        page-break-inside: avoid;
      }
      
      ul, ol {
        padding-left: 2em;
        margin-bottom: 16px;
      }
      
      li {
        margin-bottom: 0.25em;
      }
      
      code {
        padding: 0.2em 0.4em;
        margin: 0;
        font-size: 85%;
        background-color: rgba(27, 31, 35, 0.05);
        border-radius: 3px;
        font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
      }
      
      pre {
        padding: 16px;
        overflow: auto;
        font-size: 85%;
        line-height: 1.45;
        background-color: #f6f8fa;
        border-radius: 6px;
        margin-bottom: 16px;
        page-break-inside: avoid;
      }
      
      pre code {
        background-color: transparent;
        border: 0;
        display: inline;
        line-height: inherit;
        margin: 0;
        max-width: auto;
        overflow: visible;
        padding: 0;
        word-wrap: normal;
      }
      
      table {
        border-spacing: 0;
        border-collapse: collapse;
        margin-bottom: 16px;
        width: 100%;
        page-break-inside: avoid;
      }
      
      table th,
      table td {
        padding: 6px 13px;
        border: 1px solid #dfe2e5;
      }
      
      table th {
        font-weight: 600;
        background-color: #f6f8fa;
      }
      
      table tr:nth-child(2n) {
        background-color: #f6f8fa;
      }
      
      img {
        max-width: 100%;
        height: auto;
        page-break-inside: avoid;
      }
      
      hr {
        height: 0.25em;
        padding: 0;
        margin: 24px 0;
        background-color: #e1e4e8;
        border: 0;
      }
      
      .page-break {
        page-break-before: always;
      }
      
      .no-break {
        page-break-inside: avoid;
      }
      
      .toc {
        margin-bottom: 32px;
        padding: 16px;
        background-color: #f6f8fa;
        border-radius: 6px;
      }
      
      .toc h2 {
        margin-top: 0;
        border-bottom: none;
        padding-bottom: 0;
      }
      
      .toc ul {
        list-style: none;
        padding-left: 0;
      }
      
      .toc li {
        margin-bottom: 8px;
      }
      
      .toc a {
        text-decoration: none;
        color: #0969da;
      }
      
      .toc a:hover {
        text-decoration: underline;
      }
      
      .toc .toc-level-1 {
        font-weight: 600;
      }
      
      .toc .toc-level-2 {
        padding-left: 20px;
      }
      
      .toc .toc-level-3 {
        padding-left: 40px;
      }
    </style>
  `;

  public static async exportToPDF(
    markdownContent: string,
    filename: string,
    options: Partial<PDFExportOptions> = {}
  ): Promise<void> {
    const finalOptions = { ...this.DEFAULT_OPTIONS, ...options };
    
    try {
      // Convert markdown to HTML
      const htmlContent = marked(markdownContent);
      
      // Generate table of contents if requested
      let tocHtml = '';
      if (finalOptions.includeTableOfContents) {
        tocHtml = this.generateTableOfContents(markdownContent);
      }
      
      // Create complete HTML document
      const fullHtml = this.createHTMLDocument(htmlContent, tocHtml, finalOptions);
      
      // Request PDF generation from main process
      if (window.electronAPI) {
        await window.electronAPI.generatePDF(fullHtml, filename, finalOptions);
      } else {
        throw new Error('Electron API not available');
      }
    } catch (error) {
      console.error('PDF export failed:', error);
      throw error;
    }
  }

  private static generateTableOfContents(markdownContent: string): string {
    const headings: Array<{ level: number; text: string; id: string }> = [];
    const lines = markdownContent.split('\n');
    
    lines.forEach((line, index) => {
      const match = line.match(/^(#{1,6})\s+(.+)$/);
      if (match) {
        const level = match[1].length;
        const text = match[2].trim();
        const id = `heading-${index}`;
        headings.push({ level, text, id });
      }
    });
    
    if (headings.length === 0) {
      return '';
    }
    
    let tocHtml = '<div class="toc"><h2>Table of Contents</h2><ul>';
    
    headings.forEach(heading => {
      tocHtml += `<li class="toc-level-${heading.level}">
        <a href="#${heading.id}">${heading.text}</a>
      </li>`;
    });
    
    tocHtml += '</ul></div>';
    
    return tocHtml;
  }

  private static createHTMLDocument(
    content: string,
    toc: string,
    options: PDFExportOptions
  ): string {
    return `
      <!DOCTYPE html>
      <html lang="en">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>${options.title}</title>
        ${this.PDF_STYLES}
      </head>
      <body>
        ${toc}
        ${content}
      </body>
      </html>
    `;
  }
}
