import React, { useState } from 'react';
import { useAppStore } from '../../stores/appStore';
import './FileTree.css';

interface FileTreeItemProps {
  file: any;
  level: number;
  onFileClick: (file: any) => void;
  onDirectoryToggle: (path: string) => void;
  expandedDirs: Set<string>;
}

const FileTreeItem: React.FC<FileTreeItemProps> = ({
  file,
  level,
  onFileClick,
  onDirectoryToggle,
  expandedDirs
}) => {
  const isExpanded = expandedDirs.has(file.path);

  const getFileIcon = (file: any) => {
    if (file.isDirectory) {
      return isExpanded ? '📂' : '📁';
    }

    const extension = file.name.split('.').pop()?.toLowerCase();
    switch (extension) {
      case 'md':
      case 'markdown':
        return '📝';
      case 'txt':
        return '📄';
      case 'js':
      case 'ts':
      case 'tsx':
        return '📜';
      case 'json':
        return '⚙️';
      case 'css':
        return '🎨';
      case 'html':
        return '🌐';
      case 'png':
      case 'jpg':
      case 'jpeg':
      case 'gif':
      case 'svg':
        return '🖼️';
      default:
        return '📄';
    }
  };

  const handleClick = () => {
    if (file.isDirectory) {
      onDirectoryToggle(file.path);
    } else {
      onFileClick(file);
    }
  };

  return (
    <div
      className={`file-tree__item ${!file.isDirectory ? 'file-tree__item--file' : ''}`}
      onClick={handleClick}
      style={{ paddingLeft: `${level * 16 + 16}px` }}
    >
      <span className="file-tree__icon">{getFileIcon(file)}</span>
      <span className="file-tree__name">{file.name}</span>
    </div>
  );
};

const FileTree: React.FC = () => {
  const { fileTree, currentWorkspace, openFile, loadFileTree } = useAppStore();
  const [expandedDirs, setExpandedDirs] = useState<Set<string>>(new Set());

  const handleFileClick = (file: any) => {
    if (!file.isDirectory) {
      openFile(file);
    }
  };

  const handleDirectoryToggle = async (dirPath: string) => {
    const newExpandedDirs = new Set(expandedDirs);

    if (expandedDirs.has(dirPath)) {
      newExpandedDirs.delete(dirPath);
    } else {
      newExpandedDirs.add(dirPath);
      // Load directory contents if not already loaded
      await loadFileTree(dirPath);
    }

    setExpandedDirs(newExpandedDirs);
  };

  const renderFileTree = (files: any[], level: number = 0): React.ReactNode[] => {
    return files.map((file, index) => (
      <React.Fragment key={`${file.path}-${index}`}>
        <FileTreeItem
          file={file}
          level={level}
          onFileClick={handleFileClick}
          onDirectoryToggle={handleDirectoryToggle}
          expandedDirs={expandedDirs}
        />
        {file.isDirectory && expandedDirs.has(file.path) && file.children && (
          renderFileTree(file.children, level + 1)
        )}
      </React.Fragment>
    ));
  };

  if (!currentWorkspace) {
    return (
      <div className="file-tree__empty">
        <p>No workspace opened</p>
        <p>Use File → Open Folder to get started</p>
      </div>
    );
  }

  return (
    <div className="file-tree">
      <div className="file-tree__workspace">
        <div className="file-tree__workspace-name">
          📁 {currentWorkspace.split('/').pop()}
        </div>
      </div>

      <div className="file-tree__items">
        {renderFileTree(fileTree)}
      </div>
    </div>
  );
};

export default FileTree;
