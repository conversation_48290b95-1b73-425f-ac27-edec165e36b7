import React, { useMemo } from 'react';
import { marked } from 'marked';
import { useAppStore } from '../../stores/appStore';
import './Preview.css';

// Configure marked for better Markdown support
marked.setOptions({
  breaks: true,
  gfm: true,
  headerIds: true,
  mangle: false,
  sanitize: false,
});

// Custom renderer for enhanced features
const renderer = new marked.Renderer();

// Enhanced table rendering
renderer.table = function(header: string, body: string) {
  return `<div class="table-wrapper">
    <table class="markdown-table">
      <thead>${header}</thead>
      <tbody>${body}</tbody>
    </table>
  </div>`;
};

// Enhanced code block rendering
renderer.code = function(code: string, language?: string) {
  const validLang = language && language.match(/^[a-zA-Z0-9_+-]*$/);
  const langClass = validLang ? ` class="language-${language}"` : '';

  return `<div class="code-block">
    ${language ? `<div class="code-header">${language}</div>` : ''}
    <pre><code${langClass}>${code}</code></pre>
  </div>`;
};

// Enhanced blockquote rendering
renderer.blockquote = function(quote: string) {
  return `<blockquote class="markdown-blockquote">${quote}</blockquote>`;
};

// Task list support
renderer.listitem = function(text: string, task?: boolean, checked?: boolean) {
  if (task) {
    const checkedAttr = checked ? 'checked' : '';
    const checkedClass = checked ? 'checked' : '';
    return `<li class="task-list-item ${checkedClass}">
      <input type="checkbox" ${checkedAttr} disabled> ${text}
    </li>`;
  }
  return `<li>${text}</li>`;
};

marked.use({ renderer });

const Preview: React.FC = () => {
  const { editorContent, openFiles, activeFileIndex } = useAppStore();

  const htmlContent = useMemo(() => {
    if (!editorContent) return '';

    try {
      let processedContent = editorContent;

      // Process task lists
      processedContent = processedContent.replace(
        /^(\s*)-\s+\[([ x])\]\s+(.+)$/gm,
        (match, indent, checked, text) => {
          const isChecked = checked.toLowerCase() === 'x';
          return `${indent}- [${checked}] ${text}`;
        }
      );

      // Process math expressions (basic support)
      processedContent = processedContent.replace(
        /\$\$([^$]+)\$\$/g,
        '<div class="math-block">$1</div>'
      );

      processedContent = processedContent.replace(
        /\$([^$]+)\$/g,
        '<span class="math-inline">$1</span>'
      );

      // Process footnotes (basic support)
      processedContent = processedContent.replace(
        /\[\^([^\]]+)\]/g,
        '<sup class="footnote-ref"><a href="#fn-$1">$1</a></sup>'
      );

      return marked(processedContent);
    } catch (error) {
      console.error('Error parsing markdown:', error);
      return '<p class="error">Error parsing markdown content</p>';
    }
  }, [editorContent]);

  if (openFiles.length === 0 || activeFileIndex === -1) {
    return (
      <div className="preview preview--empty">
        <div className="preview__placeholder">
          <h3>Preview</h3>
          <p>Markdown preview will appear here when you open a file</p>
        </div>
      </div>
    );
  }

  return (
    <div className="preview">
      <div className="preview__header">
        <h3>Preview</h3>
      </div>
      <div className="preview__content">
        <div 
          className="preview__html"
          dangerouslySetInnerHTML={{ __html: htmlContent }}
        />
      </div>
    </div>
  );
};

export default Preview;
