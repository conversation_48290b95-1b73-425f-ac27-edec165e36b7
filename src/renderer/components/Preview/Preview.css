.preview {
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #ffffff;
}

.preview--empty {
  justify-content: center;
  align-items: center;
  background-color: #fafbfc;
}

.preview__placeholder {
  text-align: center;
  color: #6a737d;
}

.preview__placeholder h3 {
  font-size: 18px;
  margin-bottom: 8px;
  color: #24292e;
}

.preview__header {
  padding: 12px 16px;
  border-bottom: 1px solid #e1e4e8;
  background-color: #f6f8fa;
}

.preview__header h3 {
  font-size: 14px;
  font-weight: 600;
  color: #24292e;
}

.preview__content {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
}

.preview__html {
  max-width: none;
  line-height: 1.6;
  color: #24292e;
}

/* Markdown content styles */
.preview__html h1,
.preview__html h2,
.preview__html h3,
.preview__html h4,
.preview__html h5,
.preview__html h6 {
  margin-top: 24px;
  margin-bottom: 16px;
  font-weight: 600;
  line-height: 1.25;
}

.preview__html h1 {
  font-size: 2em;
  border-bottom: 1px solid #eaecef;
  padding-bottom: 0.3em;
}

.preview__html h2 {
  font-size: 1.5em;
  border-bottom: 1px solid #eaecef;
  padding-bottom: 0.3em;
}

.preview__html h3 {
  font-size: 1.25em;
}

.preview__html h4 {
  font-size: 1em;
}

.preview__html h5 {
  font-size: 0.875em;
}

.preview__html h6 {
  font-size: 0.85em;
  color: #6a737d;
}

.preview__html p {
  margin-bottom: 16px;
}

.preview__html blockquote {
  padding: 0 1em;
  color: #6a737d;
  border-left: 0.25em solid #dfe2e5;
  margin: 0 0 16px 0;
}

.preview__html ul,
.preview__html ol {
  padding-left: 2em;
  margin-bottom: 16px;
}

.preview__html li {
  margin-bottom: 0.25em;
}

.preview__html code {
  padding: 0.2em 0.4em;
  margin: 0;
  font-size: 85%;
  background-color: rgba(27, 31, 35, 0.05);
  border-radius: 3px;
  font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
}

.preview__html pre {
  padding: 16px;
  overflow: auto;
  font-size: 85%;
  line-height: 1.45;
  background-color: #f6f8fa;
  border-radius: 6px;
  margin-bottom: 16px;
}

.preview__html pre code {
  background-color: transparent;
  border: 0;
  display: inline;
  line-height: inherit;
  margin: 0;
  max-width: auto;
  overflow: visible;
  padding: 0;
  word-wrap: normal;
}

.preview__html table {
  border-spacing: 0;
  border-collapse: collapse;
  margin-bottom: 16px;
  width: 100%;
}

.preview__html table th,
.preview__html table td {
  padding: 6px 13px;
  border: 1px solid #dfe2e5;
}

.preview__html table th {
  font-weight: 600;
  background-color: #f6f8fa;
}

.preview__html table tr:nth-child(2n) {
  background-color: #f6f8fa;
}

.preview__html img {
  max-width: 100%;
  height: auto;
  border-radius: 6px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.preview__html hr {
  height: 0.25em;
  padding: 0;
  margin: 24px 0;
  background-color: #e1e4e8;
  border: 0;
}

/* Enhanced table styles */
.table-wrapper {
  overflow-x: auto;
  margin: 16px 0;
}

.markdown-table {
  border-spacing: 0;
  border-collapse: collapse;
  margin: 0;
  width: 100%;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border-radius: 6px;
  overflow: hidden;
}

.markdown-table th,
.markdown-table td {
  padding: 12px 16px;
  border: 1px solid #dfe2e5;
  text-align: left;
}

.markdown-table th {
  font-weight: 600;
  background-color: #f6f8fa;
  border-bottom: 2px solid #dfe2e5;
}

.markdown-table tr:nth-child(2n) {
  background-color: #f6f8fa;
}

.markdown-table tr:hover {
  background-color: #f1f3f4;
}

/* Enhanced code block styles */
.code-block {
  margin: 16px 0;
  border-radius: 6px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.code-header {
  background-color: #f1f3f4;
  padding: 8px 16px;
  font-size: 12px;
  font-weight: 600;
  color: #586069;
  border-bottom: 1px solid #e1e4e8;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.code-block pre {
  margin: 0;
  background-color: #f6f8fa;
  padding: 16px;
  overflow-x: auto;
}

.code-block code {
  background-color: transparent;
  padding: 0;
  border-radius: 0;
}

/* Enhanced blockquote styles */
.markdown-blockquote {
  margin: 16px 0;
  padding: 16px 20px;
  background-color: #f6f8fa;
  border-left: 4px solid #0969da;
  border-radius: 0 6px 6px 0;
  color: #656d76;
  font-style: italic;
}

.markdown-blockquote p:last-child {
  margin-bottom: 0;
}

/* Task list styles */
.task-list-item {
  list-style: none;
  margin-left: -20px;
  padding-left: 24px;
  position: relative;
}

.task-list-item input[type="checkbox"] {
  margin-right: 8px;
  margin-left: -20px;
  position: absolute;
  left: 0;
  top: 4px;
}

.task-list-item.checked {
  color: #656d76;
  text-decoration: line-through;
}

/* Math expressions */
.math-block {
  margin: 16px 0;
  padding: 16px;
  background-color: #f6f8fa;
  border-radius: 6px;
  text-align: center;
  font-family: 'Times New Roman', serif;
  font-size: 16px;
  border: 1px solid #e1e4e8;
}

.math-inline {
  background-color: rgba(175, 184, 193, 0.2);
  padding: 2px 4px;
  border-radius: 3px;
  font-family: 'Times New Roman', serif;
}

/* Footnotes */
.footnote-ref {
  font-size: 0.8em;
}

.footnote-ref a {
  color: #0969da;
  text-decoration: none;
}

.footnote-ref a:hover {
  text-decoration: underline;
}

/* Error message */
.error {
  color: #d73a49;
  background-color: #ffeef0;
  padding: 12px;
  border-radius: 6px;
  border: 1px solid #fdaeb7;
}
