import React from 'react';
import { useAppStore } from '../../stores/appStore';
import './Toolbar.css';

const Toolbar: React.FC = () => {
  const { 
    sidebarVisible, 
    previewVisible, 
    toggleSidebar, 
    togglePreview, 
    openThemeDialog 
  } = useAppStore();

  return (
    <div className="toolbar">
      <div className="toolbar__section">
        <button
          className={`toolbar__button ${sidebarVisible ? 'toolbar__button--active' : ''}`}
          onClick={toggleSidebar}
          title="Toggle Sidebar"
        >
          📁
        </button>
        
        <button
          className={`toolbar__button ${previewVisible ? 'toolbar__button--active' : ''}`}
          onClick={togglePreview}
          title="Toggle Preview"
        >
          👁️
        </button>
      </div>
      
      <div className="toolbar__section">
        <button
          className="toolbar__button"
          onClick={openThemeDialog}
          title="Change Theme"
        >
          🎨
        </button>
      </div>
    </div>
  );
};

export default Toolbar;
