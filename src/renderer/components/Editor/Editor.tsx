import React, { useCallback, useMemo } from 'react';
import CodeMirror from '@uiw/react-codemirror';
import { markdown } from '@codemirror/lang-markdown';
import { oneDark } from '@codemirror/theme-one-dark';
import { EditorView } from '@codemirror/view';
import { EditorState } from '@codemirror/state';
import { useAppStore } from '../../stores/appStore';
import './Editor.css';

const Editor: React.FC = () => {
  const { editorContent, updateFileContent, currentTheme, openFiles, activeFileIndex } = useAppStore();

  const handleChange = useCallback((value: string) => {
    updateFileContent(value);
  }, [updateFileContent]);

  const extensions = useMemo(() => [
    markdown(),
    EditorView.theme({
      '&': {
        fontSize: '14px',
        fontFamily: '"SF Mono", Monaco, "Cascadia Code", "Roboto Mono", Consolas, "Courier New", monospace',
      },
      '.cm-content': {
        padding: '16px',
        lineHeight: '1.6',
        minHeight: '100%',
      },
      '.cm-focused': {
        outline: 'none',
      },
      '.cm-editor': {
        height: '100%',
      },
      '.cm-scroller': {
        fontFamily: 'inherit',
      },
      '.cm-line': {
        padding: '0 4px',
      },
      // Markdown-specific styling
      '.cm-header': {
        fontWeight: 'bold',
      },
      '.cm-header1': {
        fontSize: '1.8em',
        color: '#0969da',
      },
      '.cm-header2': {
        fontSize: '1.5em',
        color: '#0969da',
      },
      '.cm-header3': {
        fontSize: '1.25em',
        color: '#0969da',
      },
      '.cm-strong': {
        fontWeight: 'bold',
      },
      '.cm-emphasis': {
        fontStyle: 'italic',
      },
      '.cm-strikethrough': {
        textDecoration: 'line-through',
      },
      '.cm-code': {
        backgroundColor: 'rgba(175, 184, 193, 0.2)',
        padding: '2px 4px',
        borderRadius: '3px',
        fontFamily: 'monospace',
      },
      '.cm-link': {
        color: '#0969da',
        textDecoration: 'underline',
      },
      '.cm-quote': {
        color: '#656d76',
        fontStyle: 'italic',
      },
    }),
    EditorView.lineWrapping,
  ], []);

  const theme = currentTheme === 'dark' ? oneDark : undefined;

  if (openFiles.length === 0 || activeFileIndex === -1) {
    return (
      <div className="editor editor--empty">
        <div className="editor__welcome">
          <h2>Welcome to StdMD</h2>
          <p>A powerful cross-platform Markdown editor</p>
          <div className="editor__features">
            <ul>
              <li>📝 Multi-file editing with directory structure</li>
              <li>🎨 Multiple themes and styles</li>
              <li>📄 PDF export functionality</li>
              <li>📋 Microsoft Word import</li>
              <li>🔧 MultiMarkdown & Extended syntax support</li>
            </ul>
          </div>
          <p>Open a file or create a new one to get started!</p>
        </div>
      </div>
    );
  }

  return (
    <div className="editor">
      <div className="editor__container">
        <CodeMirror
          value={editorContent}
          onChange={handleChange}
          extensions={extensions}
          theme={theme}
          placeholder="Start typing your markdown content..."
          basicSetup={{
            lineNumbers: true,
            foldGutter: true,
            dropCursor: false,
            allowMultipleSelections: false,
            indentOnInput: true,
            bracketMatching: true,
            closeBrackets: true,
            autocompletion: true,
            highlightSelectionMatches: false,
            searchKeymap: true,
            tabSize: 2,
          }}
          height="100%"
        />
      </div>
    </div>
  );
};

export default Editor;
