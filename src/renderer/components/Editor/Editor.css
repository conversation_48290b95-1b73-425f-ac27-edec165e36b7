.editor {
  height: 100%;
  width: 100%;
  background-color: var(--editor-background, #ffffff);
  display: flex;
  flex-direction: column;
}

.editor--empty {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #fafbfc;
}

.editor__welcome {
  text-align: center;
  max-width: 500px;
  padding: 40px;
}

.editor__welcome h2 {
  font-size: 28px;
  color: #24292e;
  margin-bottom: 16px;
  font-weight: 600;
}

.editor__welcome > p {
  font-size: 16px;
  color: #6a737d;
  margin-bottom: 24px;
}

.editor__features {
  margin: 24px 0;
}

.editor__features ul {
  list-style: none;
  text-align: left;
}

.editor__features li {
  padding: 8px 0;
  font-size: 14px;
  color: #24292e;
}

.editor__container {
  height: 100%;
  width: 100%;
  flex: 1;
}

.editor__container .cm-editor {
  height: 100%;
  font-size: 14px;
  font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
}

.editor__container .cm-content {
  padding: 16px;
  line-height: 1.6;
}

.editor__container .cm-focused {
  outline: none;
}

.editor__container .cm-scroller {
  font-family: inherit;
}
