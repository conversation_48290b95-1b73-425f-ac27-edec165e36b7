.theme-selector-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.theme-selector {
  background-color: var(--color-background, #ffffff);
  border-radius: 12px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  width: 90%;
  max-width: 600px;
  max-height: 80vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.theme-selector__header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 24px;
  border-bottom: 1px solid var(--color-border, #e1e4e8);
}

.theme-selector__header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: var(--color-text, #24292e);
}

.theme-selector__close {
  background: none;
  border: none;
  font-size: 24px;
  color: var(--color-textSecondary, #656d76);
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: background-color 0.1s ease;
}

.theme-selector__close:hover {
  background-color: var(--color-surface, #f6f8fa);
}

.theme-selector__content {
  flex: 1;
  padding: 24px;
  overflow-y: auto;
}

.theme-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.theme-card {
  border: 2px solid var(--color-border, #e1e4e8);
  border-radius: 8px;
  padding: 16px;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
  background-color: var(--color-background, #ffffff);
}

.theme-card:hover {
  border-color: var(--color-primary, #0969da);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.theme-card--selected {
  border-color: var(--color-primary, #0969da);
  background-color: var(--color-surface, #f6f8fa);
}

.theme-preview {
  margin-bottom: 12px;
  border-radius: 6px;
  overflow: hidden;
  height: 80px;
  border: 1px solid #e1e4e8;
}

.theme-preview__background {
  height: 100%;
  padding: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.theme-preview__surface {
  width: 100%;
  height: 100%;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 12px;
  position: relative;
}

.theme-preview__text {
  font-size: 18px;
  font-weight: 600;
}

.theme-preview__accent {
  width: 20px;
  height: 20px;
  border-radius: 50%;
}

.theme-info {
  text-align: center;
}

.theme-name {
  margin: 0 0 4px 0;
  font-size: 14px;
  font-weight: 600;
  color: var(--color-text, #24292e);
}

.theme-description {
  margin: 0;
  font-size: 12px;
  color: var(--color-textSecondary, #656d76);
  line-height: 1.4;
}

.theme-selected-indicator {
  position: absolute;
  top: 8px;
  right: 8px;
  width: 24px;
  height: 24px;
  background-color: var(--color-primary, #0969da);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: bold;
}

.theme-selector__footer {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 12px;
  padding: 20px 24px;
  border-top: 1px solid var(--color-border, #e1e4e8);
  background-color: var(--color-surface, #f6f8fa);
}

.btn {
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.1s ease;
  border: 1px solid transparent;
}

.btn--primary {
  background-color: var(--color-primary, #0969da);
  color: white;
}

.btn--primary:hover {
  background-color: var(--color-accent, #0969da);
  transform: translateY(-1px);
}

.btn--secondary {
  background-color: var(--color-background, #ffffff);
  color: var(--color-text, #24292e);
  border-color: var(--color-border, #e1e4e8);
}

.btn--secondary:hover {
  background-color: var(--color-surface, #f6f8fa);
}
