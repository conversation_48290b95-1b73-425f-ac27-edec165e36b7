import React, { useState } from 'react';
import { useAppStore } from '../../stores/appStore';
import { themes, getThemeById, applyTheme } from '../../themes/themes';
import './ThemeSelector.css';

interface ThemeSelectorProps {
  isOpen: boolean;
  onClose: () => void;
}

const ThemeSelector: React.FC<ThemeSelectorProps> = ({ isOpen, onClose }) => {
  const { currentTheme, setTheme } = useAppStore();
  const [selectedTheme, setSelectedTheme] = useState(currentTheme);

  const handleThemeSelect = (themeId: string) => {
    setSelectedTheme(themeId);
    const theme = getThemeById(themeId);
    applyTheme(theme);
    setTheme(themeId);
  };

  const handleClose = () => {
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="theme-selector-overlay" onClick={handleClose}>
      <div className="theme-selector" onClick={(e) => e.stopPropagation()}>
        <div className="theme-selector__header">
          <h3>Choose Theme</h3>
          <button className="theme-selector__close" onClick={handleClose}>
            ×
          </button>
        </div>
        
        <div className="theme-selector__content">
          <div className="theme-grid">
            {themes.map((theme) => (
              <div
                key={theme.id}
                className={`theme-card ${selectedTheme === theme.id ? 'theme-card--selected' : ''}`}
                onClick={() => handleThemeSelect(theme.id)}
              >
                <div className="theme-preview">
                  <div 
                    className="theme-preview__background"
                    style={{ backgroundColor: theme.colors.background }}
                  >
                    <div 
                      className="theme-preview__surface"
                      style={{ backgroundColor: theme.colors.surface }}
                    >
                      <div 
                        className="theme-preview__text"
                        style={{ color: theme.colors.text }}
                      >
                        Aa
                      </div>
                      <div 
                        className="theme-preview__accent"
                        style={{ backgroundColor: theme.colors.primary }}
                      />
                    </div>
                  </div>
                </div>
                
                <div className="theme-info">
                  <h4 className="theme-name">{theme.name}</h4>
                  <p className="theme-description">{theme.description}</p>
                </div>
                
                {selectedTheme === theme.id && (
                  <div className="theme-selected-indicator">
                    ✓
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
        
        <div className="theme-selector__footer">
          <button className="btn btn--secondary" onClick={handleClose}>
            Cancel
          </button>
          <button className="btn btn--primary" onClick={handleClose}>
            Apply
          </button>
        </div>
      </div>
    </div>
  );
};

export default ThemeSelector;
