import React, { useState } from 'react';
import { PDFExportService, PDFExportOptions } from '../../services/pdfExport';
import { useAppStore } from '../../stores/appStore';
import './PDFExportDialog.css';

interface PDFExportDialogProps {
  isOpen: boolean;
  onClose: () => void;
}

const PDFExportDialog: React.FC<PDFExportDialogProps> = ({ isOpen, onClose }) => {
  const { editorContent, openFiles, activeFileIndex } = useAppStore();
  const [isExporting, setIsExporting] = useState(false);
  const [exportOptions, setExportOptions] = useState<Partial<PDFExportOptions>>({
    title: 'Markdown Document',
    author: 'StdMD',
    format: 'A4',
    orientation: 'portrait',
    includeTableOfContents: false,
    margins: {
      top: '1in',
      right: '1in',
      bottom: '1in',
      left: '1in',
    },
  });

  const currentFile = openFiles[activeFileIndex];
  const defaultFilename = currentFile ? 
    currentFile.name.replace(/\.(md|markdown)$/i, '.pdf') : 
    'document.pdf';

  const handleExport = async () => {
    if (!editorContent) {
      alert('No content to export');
      return;
    }

    setIsExporting(true);
    try {
      await PDFExportService.exportToPDF(
        editorContent,
        defaultFilename,
        exportOptions
      );
      alert('PDF exported successfully!');
      onClose();
    } catch (error) {
      console.error('Export failed:', error);
      alert('Failed to export PDF: ' + (error as Error).message);
    } finally {
      setIsExporting(false);
    }
  };

  const handleOptionChange = (key: keyof PDFExportOptions, value: any) => {
    setExportOptions(prev => ({
      ...prev,
      [key]: value,
    }));
  };

  const handleMarginChange = (side: string, value: string) => {
    setExportOptions(prev => ({
      ...prev,
      margins: {
        ...prev.margins,
        [side]: value,
      },
    }));
  };

  if (!isOpen) return null;

  return (
    <div className="pdf-export-overlay" onClick={onClose}>
      <div className="pdf-export-dialog" onClick={(e) => e.stopPropagation()}>
        <div className="pdf-export-dialog__header">
          <h3>Export to PDF</h3>
          <button className="pdf-export-dialog__close" onClick={onClose}>
            ×
          </button>
        </div>

        <div className="pdf-export-dialog__content">
          <div className="form-group">
            <label htmlFor="title">Document Title</label>
            <input
              id="title"
              type="text"
              value={exportOptions.title || ''}
              onChange={(e) => handleOptionChange('title', e.target.value)}
              placeholder="Enter document title"
            />
          </div>

          <div className="form-group">
            <label htmlFor="author">Author</label>
            <input
              id="author"
              type="text"
              value={exportOptions.author || ''}
              onChange={(e) => handleOptionChange('author', e.target.value)}
              placeholder="Enter author name"
            />
          </div>

          <div className="form-row">
            <div className="form-group">
              <label htmlFor="format">Page Format</label>
              <select
                id="format"
                value={exportOptions.format || 'A4'}
                onChange={(e) => handleOptionChange('format', e.target.value)}
              >
                <option value="A4">A4</option>
                <option value="Letter">Letter</option>
                <option value="Legal">Legal</option>
              </select>
            </div>

            <div className="form-group">
              <label htmlFor="orientation">Orientation</label>
              <select
                id="orientation"
                value={exportOptions.orientation || 'portrait'}
                onChange={(e) => handleOptionChange('orientation', e.target.value)}
              >
                <option value="portrait">Portrait</option>
                <option value="landscape">Landscape</option>
              </select>
            </div>
          </div>

          <div className="form-group">
            <label className="checkbox-label">
              <input
                type="checkbox"
                checked={exportOptions.includeTableOfContents || false}
                onChange={(e) => handleOptionChange('includeTableOfContents', e.target.checked)}
              />
              Include Table of Contents
            </label>
          </div>

          <div className="form-group">
            <label>Margins</label>
            <div className="margins-grid">
              <div className="margin-input">
                <label htmlFor="margin-top">Top</label>
                <input
                  id="margin-top"
                  type="text"
                  value={exportOptions.margins?.top || '1in'}
                  onChange={(e) => handleMarginChange('top', e.target.value)}
                  placeholder="1in"
                />
              </div>
              <div className="margin-input">
                <label htmlFor="margin-right">Right</label>
                <input
                  id="margin-right"
                  type="text"
                  value={exportOptions.margins?.right || '1in'}
                  onChange={(e) => handleMarginChange('right', e.target.value)}
                  placeholder="1in"
                />
              </div>
              <div className="margin-input">
                <label htmlFor="margin-bottom">Bottom</label>
                <input
                  id="margin-bottom"
                  type="text"
                  value={exportOptions.margins?.bottom || '1in'}
                  onChange={(e) => handleMarginChange('bottom', e.target.value)}
                  placeholder="1in"
                />
              </div>
              <div className="margin-input">
                <label htmlFor="margin-left">Left</label>
                <input
                  id="margin-left"
                  type="text"
                  value={exportOptions.margins?.left || '1in'}
                  onChange={(e) => handleMarginChange('left', e.target.value)}
                  placeholder="1in"
                />
              </div>
            </div>
          </div>
        </div>

        <div className="pdf-export-dialog__footer">
          <button 
            className="btn btn--secondary" 
            onClick={onClose}
            disabled={isExporting}
          >
            Cancel
          </button>
          <button 
            className="btn btn--primary" 
            onClick={handleExport}
            disabled={isExporting || !editorContent}
          >
            {isExporting ? 'Exporting...' : 'Export PDF'}
          </button>
        </div>
      </div>
    </div>
  );
};

export default PDFExportDialog;
