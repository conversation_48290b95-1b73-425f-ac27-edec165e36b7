import React from 'react';
import Sidebar from '../Sidebar/Sidebar';
import Editor from '../Editor/Editor';
import Preview from '../Preview/Preview';
import TabBar from '../TabBar/TabBar';
import Toolbar from '../Toolbar/Toolbar';
import ThemeSelector from '../ThemeSelector/ThemeSelector';
import { useAppStore } from '../../stores/appStore';
import './Layout.css';

const Layout: React.FC = () => {
  const { sidebarVisible, previewVisible, themeDialogOpen, closeThemeDialog } = useAppStore();

  return (
    <div className="layout">
      <div className="layout__toolbar">
        <Toolbar />
      </div>

      <div className="layout__body">
        {sidebarVisible && (
          <div className="layout__sidebar">
            <Sidebar />
          </div>
        )}

        <div className="layout__main">
          <div className="layout__tabs">
            <TabBar />
          </div>

          <div className="layout__content">
            <div className="layout__editor">
              <Editor />
            </div>

            {previewVisible && (
              <div className="layout__preview">
                <Preview />
              </div>
            )}
          </div>
        </div>
      </div>

      <ThemeSelector
        isOpen={themeDialogOpen}
        onClose={closeThemeDialog}
      />
    </div>
  );
};

export default Layout;
