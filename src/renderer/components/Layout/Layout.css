.layout {
  display: flex;
  flex-direction: column;
  height: 100vh;
  width: 100vw;
  background-color: var(--color-background);
}

.layout__toolbar {
  flex-shrink: 0;
}

.layout__body {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.layout__sidebar {
  width: 250px;
  min-width: 200px;
  max-width: 400px;
  background-color: var(--color-surface);
  border-right: 1px solid var(--color-border);
  resize: horizontal;
  overflow: hidden;
}

.layout__main {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.layout__tabs {
  height: 40px;
  background-color: var(--color-surface);
  border-bottom: 1px solid var(--color-border);
}

.layout__content {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.layout__editor {
  flex: 1;
  min-width: 0;
  background-color: var(--editor-background);
}

.layout__preview {
  width: 50%;
  min-width: 300px;
  border-left: 1px solid var(--color-border);
  background-color: var(--preview-background);
}
