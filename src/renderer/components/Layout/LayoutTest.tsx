import React from 'react';
import { useAppStore } from '../../stores/appStore';

const LayoutTest: React.FC = () => {
  const { 
    sidebarVisible, 
    previewVisible, 
    openFiles, 
    currentWorkspace,
    toggleSidebar,
    togglePreview,
    openFile
  } = useAppStore();

  const handleNewFile = () => {
    openFile({
      name: 'Test.md',
      path: '',
      isDirectory: false,
      content: '# Test Document\n\nThis is a test document to verify the editor is working.\n\n## Features\n\n- Basic editing\n- Real-time preview\n- File management\n',
    });
  };

  return (
    <div style={{ 
      height: '100vh', 
      display: 'flex', 
      flexDirection: 'column',
      fontFamily: 'system-ui, sans-serif'
    }}>
      {/* Simple toolbar */}
      <div style={{ 
        height: '40px', 
        backgroundColor: '#f0f0f0', 
        display: 'flex', 
        alignItems: 'center', 
        padding: '0 10px',
        borderBottom: '1px solid #ccc'
      }}>
        <button onClick={toggleSidebar} style={{ marginRight: '10px' }}>
          {sidebarVisible ? 'Hide' : 'Show'} Sidebar
        </button>
        <button onClick={togglePreview} style={{ marginRight: '10px' }}>
          {previewVisible ? 'Hide' : 'Show'} Preview
        </button>
        <button onClick={handleNewFile} style={{ marginRight: '10px' }}>
          New File
        </button>
        <span style={{ marginLeft: 'auto', fontSize: '12px', color: '#666' }}>
          Files: {openFiles.length} | Workspace: {currentWorkspace || 'None'}
        </span>
      </div>

      {/* Main content area */}
      <div style={{ 
        flex: 1, 
        display: 'flex',
        overflow: 'hidden'
      }}>
        {/* Sidebar */}
        {sidebarVisible && (
          <div style={{ 
            width: '250px', 
            backgroundColor: '#f8f9fa', 
            borderRight: '1px solid #ccc',
            padding: '10px'
          }}>
            <h3 style={{ margin: '0 0 10px 0', fontSize: '14px' }}>File Explorer</h3>
            {currentWorkspace ? (
              <div>
                <div style={{ fontSize: '12px', color: '#666' }}>
                  Workspace: {currentWorkspace.split('/').pop()}
                </div>
              </div>
            ) : (
              <div style={{ fontSize: '12px', color: '#999' }}>
                No workspace opened
              </div>
            )}
          </div>
        )}

        {/* Editor area */}
        <div style={{ 
          flex: 1, 
          display: 'flex',
          flexDirection: 'column'
        }}>
          {/* Tab bar */}
          <div style={{ 
            height: '35px', 
            backgroundColor: '#f6f8fa', 
            borderBottom: '1px solid #ccc',
            display: 'flex',
            alignItems: 'center',
            padding: '0 10px'
          }}>
            {openFiles.length > 0 ? (
              openFiles.map((file, index) => (
                <div key={index} style={{ 
                  padding: '5px 10px', 
                  backgroundColor: '#fff',
                  border: '1px solid #ccc',
                  marginRight: '5px',
                  fontSize: '12px'
                }}>
                  {file.name}
                </div>
              ))
            ) : (
              <span style={{ fontSize: '12px', color: '#666' }}>No files open</span>
            )}
          </div>

          {/* Content area */}
          <div style={{ 
            flex: 1, 
            display: 'flex'
          }}>
            {/* Editor */}
            <div style={{ 
              flex: 1, 
              backgroundColor: '#fff',
              padding: '20px'
            }}>
              {openFiles.length > 0 ? (
                <div>
                  <h3>Editor Area</h3>
                  <textarea 
                    style={{ 
                      width: '100%', 
                      height: '300px', 
                      border: '1px solid #ccc',
                      padding: '10px',
                      fontFamily: 'monospace'
                    }}
                    defaultValue={openFiles[0]?.content || ''}
                    placeholder="Start typing your markdown content..."
                  />
                </div>
              ) : (
                <div style={{ textAlign: 'center', marginTop: '50px' }}>
                  <h2>Welcome to StdMD</h2>
                  <p>Click "New File" to get started</p>
                </div>
              )}
            </div>

            {/* Preview */}
            {previewVisible && (
              <div style={{ 
                width: '50%', 
                backgroundColor: '#fff',
                borderLeft: '1px solid #ccc',
                padding: '20px'
              }}>
                <h3>Preview Area</h3>
                {openFiles.length > 0 ? (
                  <div style={{ 
                    border: '1px solid #eee', 
                    padding: '10px',
                    minHeight: '300px'
                  }}>
                    <div dangerouslySetInnerHTML={{ 
                      __html: openFiles[0]?.content?.replace(/\n/g, '<br>') || 'No content'
                    }} />
                  </div>
                ) : (
                  <div style={{ color: '#666' }}>
                    Preview will appear here when you open a file
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default LayoutTest;
