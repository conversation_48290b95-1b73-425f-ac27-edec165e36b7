# 快速开始

欢迎使用StdMD！这是一个功能强大的跨平台Markdown编辑器。

## 安装

### 系统要求
- Windows 10/11, macOS 10.14+, 或 Linux
- 至少 4GB RAM
- 100MB 可用磁盘空间

### 下载安装
1. 访问 [发布页面](https://github.com/stdmd/releases)
2. 下载适合您操作系统的安装包
3. 运行安装程序并按照提示操作

## 首次使用

### 1. 打开工作区
- 点击 `File → Open Folder` 或使用快捷键 `Ctrl+Shift+O`
- 选择包含Markdown文件的文件夹

### 2. 创建新文件
- 点击 `File → New File` 或使用快捷键 `Ctrl+N`
- 开始编写您的Markdown内容

### 3. 实时预览
- 编辑器右侧会自动显示渲染后的预览
- 支持实时更新，无需手动刷新

## 基本功能

### 文件管理
- 📁 文件树浏览
- 📝 多文件标签页
- 💾 自动保存
- 🔍 文件搜索

### 编辑功能
- ✨ 语法高亮
- 🔧 代码补全
- 📋 剪贴板增强
- ⚡ 快捷键支持

### 导入导出
- 📄 PDF导出
- 📎 Word文档导入
- 🖼️ 图片粘贴
- 📊 表格编辑

## 下一步

- 查看 [用户指南](user-guide.md) 了解详细功能
- 阅读 [快捷键参考](shortcuts.md) 提高效率
- 探索 [主题定制](themes.md) 个性化界面
