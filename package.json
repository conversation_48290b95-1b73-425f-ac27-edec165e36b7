{"name": "stdmd", "version": "1.0.0", "description": "A cross-platform desktop Markdown editor with advanced features", "main": "dist/main/main.js", "homepage": "./", "scripts": {"dev": "concurrently \"npm run dev:main\" \"npm run dev:renderer\"", "dev:main": "webpack --config webpack.main.config.js --mode development --watch", "dev:renderer": "webpack serve --config webpack.renderer.config.js --mode development", "build": "npm run build:main && npm run build:renderer", "build:main": "webpack --config webpack.main.config.js --mode production", "build:renderer": "webpack --config webpack.renderer.config.js --mode production", "start": "electron .", "pack": "electron-builder", "dist": "npm run build && electron-builder", "dist:win": "npm run build && electron-builder --win", "dist:mac": "npm run build && electron-builder --mac", "dist:linux": "npm run build && electron-builder --linux", "test": "jest", "lint": "eslint src --ext .ts,.tsx", "lint:fix": "eslint src --ext .ts,.tsx --fix"}, "keywords": ["markdown", "editor", "electron", "cross-platform", "pdf-export", "word-import"], "author": "StdMD Team", "license": "MIT", "devDependencies": {"@types/marked": "^5.0.2", "@types/node": "^20.0.0", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "concurrently": "^8.2.0", "css-loader": "^6.8.0", "electron": "^25.0.0", "electron-builder": "^24.0.0", "eslint": "^8.45.0", "eslint-plugin-react": "^7.33.0", "eslint-plugin-react-hooks": "^4.6.0", "html-webpack-plugin": "^5.5.0", "jest": "^29.6.0", "style-loader": "^3.3.0", "ts-loader": "^9.4.0", "typescript": "^5.1.0", "webpack": "^5.88.0", "webpack-cli": "^5.1.0", "webpack-dev-server": "^4.15.0"}, "dependencies": {"@codemirror/lang-markdown": "^6.2.0", "@codemirror/state": "^6.2.0", "@codemirror/view": "^6.15.0", "@uiw/react-codemirror": "^4.21.0", "chokidar": "^3.5.0", "mammoth": "^1.6.0", "marked": "^5.1.0", "puppeteer": "^20.7.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.14.0", "remark": "^14.0.0", "remark-gfm": "^3.0.0", "remark-html": "^15.0.0", "zustand": "^4.3.0"}, "build": {"appId": "com.stdmd.app", "productName": "StdMD", "directories": {"output": "dist"}, "files": ["dist/**/*", "node_modules/**/*"], "mac": {"category": "public.app-category.productivity"}, "win": {"target": "nsis"}, "linux": {"target": "AppImage"}}}