{"name": "stdmd", "version": "1.0.0", "description": "A cross-platform desktop Markdown editor with advanced features", "main": "dist/main/main.js", "homepage": "./", "scripts": {"dev": "concurrently \"npm run dev:main\" \"npm run dev:renderer\"", "dev:main": "webpack --config webpack.main.config.js --mode development --watch", "dev:renderer": "webpack serve --config webpack.renderer.config.js --mode development", "build": "npm run build:main && npm run build:renderer", "build:main": "webpack --config webpack.main.config.js --mode production", "build:renderer": "webpack --config webpack.renderer.config.js --mode production", "build:icons": "node scripts/build-icons.js", "start": "electron .", "pack": "npm run build && electron-builder", "dist": "npm run build && npm run build:icons && electron-builder", "dist:win": "npm run build && npm run build:icons && electron-builder --win", "dist:mac": "npm run build && npm run build:icons && electron-builder --mac", "dist:linux": "npm run build && npm run build:icons && electron-builder --linux", "dist:all": "npm run build && npm run build:icons && electron-builder --win --mac --linux", "test": "jest", "lint": "eslint src --ext .ts,.tsx", "lint:fix": "eslint src --ext .ts,.tsx --fix", "clean": "rimraf dist build/dist node_modules/.cache"}, "keywords": ["markdown", "editor", "electron", "cross-platform", "pdf-export", "word-import"], "author": "StdMD Team", "license": "MIT", "devDependencies": {"@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/marked": "^5.0.2", "@types/node": "^20.0.0", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "concurrently": "^8.2.0", "css-loader": "^6.8.0", "electron": "^25.0.0", "electron-builder": "^24.0.0", "eslint": "^8.45.0", "eslint-plugin-react": "^7.33.0", "eslint-plugin-react-hooks": "^4.6.0", "html-webpack-plugin": "^5.5.0", "identity-obj-proxy": "^3.0.0", "jest": "^29.6.0", "jest-environment-jsdom": "^29.7.0", "rimraf": "^6.0.1", "style-loader": "^3.3.0", "ts-jest": "^29.4.0", "ts-loader": "^9.4.0", "typescript": "^5.1.0", "webpack": "^5.88.0", "webpack-cli": "^5.1.0", "webpack-dev-server": "^4.15.0"}, "dependencies": {"@codemirror/lang-markdown": "^6.2.0", "@codemirror/state": "^6.2.0", "@codemirror/view": "^6.15.0", "@uiw/react-codemirror": "^4.21.0", "chokidar": "^3.5.0", "mammoth": "^1.6.0", "marked": "^5.1.0", "puppeteer": "^20.7.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.14.0", "remark": "^14.0.0", "remark-gfm": "^3.0.0", "remark-html": "^15.0.0", "zustand": "^4.3.0"}, "build": {"appId": "com.stdmd.app", "productName": "StdMD", "copyright": "Copyright © 2024 StdMD Team", "directories": {"output": "build/dist", "buildResources": "build"}, "files": ["dist/**/*", "node_modules/**/*", "!node_modules/.cache/**/*", "!**/*.{iml,o,hprof,orig,pyc,pyo,rbc,swp,csproj,sln,xproj}", "!.editorconfig", "!**/._*", "!**/{.DS_Store,.git,.hg,.svn,CVS,RCS,SCCS,.gitignore,.gitattributes}", "!**/{__pycache__,thumbs.db,.flowconfig,.idea,.vs,.nyc_output}", "!**/{appveyor.yml,.travis.yml,circle.yml}", "!**/{npm-debug.log,yarn.lock,.yarn-integrity,.yarn-metadata.json}"], "extraResources": [{"from": "docs/", "to": "docs/", "filter": ["**/*"]}, {"from": "examples/", "to": "examples/", "filter": ["**/*"]}], "mac": {"category": "public.app-category.productivity", "icon": "build/icon.icns", "target": [{"target": "dmg", "arch": ["x64", "arm64"]}, {"target": "zip", "arch": ["x64", "arm64"]}], "darkModeSupport": true}, "win": {"target": [{"target": "nsis", "arch": ["x64", "ia32"]}, {"target": "portable", "arch": ["x64"]}], "icon": "build/icon.ico", "publisherName": "StdMD Team"}, "linux": {"target": [{"target": "AppImage", "arch": ["x64"]}, {"target": "deb", "arch": ["x64"]}, {"target": "rpm", "arch": ["x64"]}], "icon": "build/icon.svg", "category": "Office", "description": "A powerful cross-platform Markdown editor with advanced features"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true, "shortcutName": "StdMD"}, "dmg": {"title": "StdMD ${version}", "icon": "build/icon.icns", "background": "build/dmg-background.png", "contents": [{"x": 130, "y": 220}, {"x": 410, "y": 220, "type": "link", "path": "/Applications"}]}, "publish": {"provider": "github", "owner": "stdmd", "repo": "stdmd"}}}