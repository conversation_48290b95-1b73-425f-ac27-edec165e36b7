# StdMD - 跨平台Markdown编辑器

一个功能强大的跨平台桌面Markdown编辑器，支持多文件编辑、实时预览、PDF导出和Word导入。

## 主要功能

### ✨ 核心特性
- 📁 **多文件管理** - 基于本地目录结构的文件浏览和管理
- ✏️ **实时编辑** - 语法高亮的Markdown编辑器
- 👀 **实时预览** - 即时查看渲染效果
- 🎨 **多种主题** - 支持不同的样式风格

### 📄 文档处理
- 📝 **MultiMarkdown支持** - 扩展的Markdown语法
- 📋 **Markdown Extended** - 更多高级功能
- 📄 **PDF导出** - 将Markdown文档导出为PDF
- 📎 **Word导入** - 从Microsoft Word文档导入内容

### 🖥️ 跨平台支持
- 🪟 Windows
- 🍎 macOS  
- 🐧 Linux

## 技术架构

### 前端技术栈
- **Electron** - 跨平台桌面应用框架
- **React** - 用户界面库
- **TypeScript** - 类型安全的JavaScript
- **CodeMirror** - 代码编辑器组件
- **Zustand** - 状态管理

### 核心依赖
- **marked** - Markdown解析器
- **puppeteer** - PDF生成
- **mammoth** - Word文档处理
- **chokidar** - 文件监听

## 开发指南

### 安装依赖
```bash
npm install
```

### 开发模式
```bash
npm run dev
```

### 构建应用
```bash
npm run build
```

### 打包分发
```bash
# 所有平台
npm run dist

# Windows
npm run dist:win

# macOS
npm run dist:mac

# Linux
npm run dist:linux
```

## 项目结构

```
StdMD/
├── src/
│   ├── main/           # Electron主进程
│   │   ├── main.ts     # 主进程入口
│   │   └── preload.ts  # 预加载脚本
│   ├── renderer/       # React渲染进程
│   │   ├── components/ # UI组件
│   │   ├── stores/     # 状态管理
│   │   └── styles/     # 样式文件
│   └── shared/         # 共享代码
├── public/             # 静态资源
├── dist/               # 构建输出
└── build/              # 打包输出
```

## 使用说明

1. **打开工作区** - 使用 `File → Open Folder` 选择工作目录
2. **创建文件** - 使用 `File → New File` 或 `Ctrl+N`
3. **编辑文档** - 在左侧编辑器中输入Markdown内容
4. **实时预览** - 右侧自动显示渲染效果
5. **导出PDF** - 使用 `File → Export as PDF`
6. **导入Word** - 使用 `File → Import Word Document`

## 快捷键

- `Ctrl+N` - 新建文件
- `Ctrl+O` - 打开文件
- `Ctrl+Shift+O` - 打开文件夹
- `Ctrl+S` - 保存文件
- `Ctrl+Shift+S` - 另存为
- `Ctrl+E` - 导出PDF

## 贡献指南

欢迎提交Issue和Pull Request来帮助改进StdMD！

## 许可证

MIT License
