const { app, BrowserWindow } = require('electron');
const path = require('path');

// 创建一个简单的测试窗口来验证UI
function createTestWindow() {
  const win = new BrowserWindow({
    width: 1200,
    height: 800,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      preload: path.join(__dirname, 'dist/main/preload.js'),
    },
    show: true,
  });

  // 加载应用
  win.loadFile(path.join(__dirname, 'dist/renderer/index.html'));
  
  // 打开开发者工具以便调试
  win.webContents.openDevTools();
  
  // 监听页面加载完成
  win.webContents.once('did-finish-load', () => {
    console.log('✅ Application loaded successfully');
    
    // 测试新建文件功能
    setTimeout(() => {
      console.log('🧪 Testing new file creation...');
      win.webContents.send('menu-new-file');
    }, 2000);
    
    // 测试主题切换
    setTimeout(() => {
      console.log('🧪 Testing theme functionality...');
      win.webContents.executeJavaScript(`
        console.log('Testing theme system...');
        if (window.useAppStore) {
          const store = window.useAppStore.getState();
          console.log('Current theme:', store.currentTheme);
          console.log('Sidebar visible:', store.sidebarVisible);
          console.log('Preview visible:', store.previewVisible);
        }
      `);
    }, 3000);
  });
  
  // 监听控制台消息
  win.webContents.on('console-message', (event, level, message) => {
    console.log(`[Renderer ${level}]:`, message);
  });
  
  return win;
}

app.whenReady().then(() => {
  console.log('🚀 Starting StdMD UI Test...');
  createTestWindow();
});

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

app.on('activate', () => {
  if (BrowserWindow.getAllWindows().length === 0) {
    createTestWindow();
  }
});
