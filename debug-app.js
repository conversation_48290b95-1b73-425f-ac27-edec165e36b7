#!/usr/bin/env node

const { spawn } = require('child_process');
const path = require('path');

console.log('🔍 StdMD Debug Launcher');
console.log('========================');

// 启动应用并捕获输出
const electronPath = path.join(__dirname, 'node_modules', '.bin', 'electron');
const appPath = path.join(__dirname, 'dist', 'main', 'main.js');

console.log('📍 Electron path:', electronPath);
console.log('📍 App path:', appPath);
console.log('');

const app = spawn(electronPath, [appPath], {
  stdio: ['inherit', 'pipe', 'pipe'],
  cwd: __dirname
});

app.stdout.on('data', (data) => {
  console.log('📤 [STDOUT]:', data.toString().trim());
});

app.stderr.on('data', (data) => {
  console.log('📤 [STDERR]:', data.toString().trim());
});

app.on('close', (code) => {
  console.log('');
  console.log('🏁 Application exited with code:', code);
});

app.on('error', (err) => {
  console.error('❌ Failed to start application:', err);
});

// 处理Ctrl+C
process.on('SIGINT', () => {
  console.log('\n🛑 Stopping application...');
  app.kill('SIGINT');
  process.exit(0);
});

console.log('🚀 Starting StdMD...');
console.log('💡 Press Ctrl+C to stop');
console.log('');
